import norMalConfig from '../normal-config';
const gateWay = '/selfanalyticsSevice';

export default {
    // 租户权限配置查询
    getTenantPermissionConfig(params) {
        return norMalConfig('/tenant/permission/configInfo', params, gateWay);
    },
    // 租户权限配置保存
    saveTenantPermissionConfig(params) {
        return norMalConfig('/tenant/permission/save', params, gateWay);
    },
    // 租户生效状态
    changeTenantValid(params) {
        return norMalConfig('/tenant/changeValid', params, gateWay);
    },
    // 租户服务配置信息
    getTenantMcpInfo(params) {
        return norMalConfig('/tenant/mcpInfo', params, gateWay);
    },
    // 租户密钥管理列表
    getTenantSecretKeyPage(params) {
        return norMalConfig('/tenant/secretKey/page', params, gateWay);
    },
    // 租户密钥生成
    generateTenantSecretKey(params) {
        return norMalConfig('/tenant/secretKey/generate', params, gateWay);
    },
    // 密钥生效状态
    changeSecretKeyValid(params) {
        return norMalConfig('/tenant/secretKey/changeValid', params, gateWay);
    },
    // 租户信息列表
    getTenantPage(params) {
        return norMalConfig('/tenant/page', params, gateWay);
    },
    // 新增租户
    addTenant(params) {
        return norMalConfig('/tenant/add', params, gateWay);
    },
    // 修改租户信息
    updateTenant(params) {
        return norMalConfig('/tenant/update', params, gateWay);
    },
    // 查询租户详情
    getTenantDetails(params) {
        return norMalConfig('/tenant/getDetails', params, gateWay);
    },
    // 刷新密钥
    updateSecretKey(params) {
        return norMalConfig('/tenant/secretKey/update', params, gateWay);
    },
    // 删除租户
    deleteTenant(params) {
        return norMalConfig('/tenant/remove', params, gateWay);
    }
};
