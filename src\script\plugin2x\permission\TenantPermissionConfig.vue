<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        top="60px"
        width="1348px"
        :close-on-click-modal="false"
        :before-close="handleClose"
        destroy-on-close
        custom-class="tenant-permission-config"
        @open="fetchList"
    >
        <header class="config-header">
            <el-input v-model="dialogSearchParams.mcpName" placeholder="按服务名称检索" clearable />
            <el-input
                v-model="dialogSearchParams.toolName"
                placeholder="按工具名称检索"
                clearable
            />
            <el-button type="primary" @click.native.stop="search">查询</el-button>
            <el-radio-group class="header-radio-group" size="small" v-model="radioValue">
                <el-radio-button
                    v-for="(item, index) in radioGroup"
                    :key="index"
                    :label="item.value"
                >
                    {{ item.label }}
                </el-radio-button>
            </el-radio-group>
        </header>
        <main class="config-main custom-scrollbar" v-if="list.length > 0">
            <div
                class="config-main-item"
                v-for="(mcp, mcpIndex) in list"
                :key="mcpIndex"
                :class="{ 'is-expand': expandList.includes(mcpIndex) }"
                @click.stop="handleOutListExpand(mcpIndex)"
            >
                <div class="item-content-header">
                    <div class="item-content-header-left">
                        <el-checkbox
                            v-model="mcp.isChoice"
                            :indeterminate="mcp.isIndeterminate_outer"
                            :true-label="1"
                            :false-label="0"
                            @click.native.stop
                            @change="mcp.handleCheckAllChange"
                            class="out-list"
                        >
                            {{ mcp.mcpName }}
                        </el-checkbox>
                        <p
                            class="item-desc"
                            :class="{ 'text-ellipsis': !expandList.includes(mcpIndex) }"
                            :title="mcp.mcpDescription"
                            @click.stop
                        >
                            {{ mcp.mcpDescription }}
                        </p>
                    </div>
                    <div class="item-content-header-right">
                        <i
                            class="el-icon-arrow-down out-list-expand-icon"
                            :class="{ rotate180: expandList.includes(mcpIndex) }"
                            @click.stop="handleOutListExpand(mcpIndex)"
                        ></i>
                    </div>
                </div>
                <div class="item-content-main" v-if="expandList.includes(mcpIndex)" @click.stop>
                    <section class="tool-list-section">
                        <el-checkbox
                            :indeterminate="mcp.isIndeterminate_tool"
                            v-model="mcp.checkAll_tool"
                            :true-label="1"
                            :false-label="0"
                            @click.native.stop
                            @change="mcp.handleCheckAllChange_tool"
                            >工具
                        </el-checkbox>
                        <div
                            class="tool-list custom-scrollbar"
                            v-if="mcp.displayToolList.length > 0"
                        >
                            <div
                                class="tool-item"
                                :class="{ 'is-choice': item.isChoice }"
                                v-for="(item, index) in mcp.displayToolList"
                                :key="index"
                            >
                                <el-checkbox
                                    v-model="item.isChoice"
                                    :true-label="1"
                                    :false-label="0"
                                    @click.native.stop
                                    @change="() => mcp.handleToolCheckChange(item)"
                                    >{{ item.toolName }}</el-checkbox
                                >
                                <p class="tool-desc text-ellipsis" :title="item.toolDescription">
                                    {{ item.toolDescription }}
                                </p>
                            </div>
                        </div>
                        <el-empty
                            v-else
                            description="暂无数据"
                            :image="require('@/img/common/noDataMon.png')"
                            style="width: 100%; height: 100%"
                        >
                        </el-empty>
                    </section>
                    <section class="resource-list-section">
                        <el-checkbox
                            :indeterminate="mcp.isIndeterminate_resource"
                            v-model="mcp.checkAll_resource"
                            :true-label="1"
                            :false-label="0"
                            @click.native.stop
                            @change="mcp.handleCheckAllChange_resource"
                            >资源
                        </el-checkbox>
                        <div
                            class="resource-list custom-scrollbar"
                            v-if="mcp.displayResourceList.length > 0"
                        >
                            <div
                                class="resource-item"
                                :class="{ 'is-choice': item.isChoice }"
                                v-for="(item, index) in mcp.displayResourceList"
                                :key="index"
                            >
                                <el-checkbox
                                    v-model="item.isChoice"
                                    :true-label="1"
                                    :false-label="0"
                                    @click.native.stop
                                    @change="() => mcp.handleResourceCheckChange(item)"
                                    >{{ item.resourceName }}</el-checkbox
                                >
                                <p
                                    class="resource-desc text-ellipsis"
                                    :title="item.resourceDescription"
                                >
                                    {{ item.resourceDescription }}
                                </p>
                            </div>
                        </div>
                        <el-empty
                            v-else
                            description="暂无数据"
                            :image="require('@/img/common/noDataMon.png')"
                            style="width: 100%; height: 100%"
                        >
                        </el-empty>
                    </section>
                </div>
            </div>
        </main>
        <el-empty
            v-else
            description="暂无数据"
            :image="require('@/img/common/noDataMon.png')"
            class="config-main"
        >
        </el-empty>
        <footer class="config-footer" slot="footer">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSuccess">确定</el-button>
        </footer>
    </el-dialog>
</template>

<script>
import Permission from '@/script/api/module/permission';

export default {
    name: 'TenantPermissionConfig',
    props: {
        // 弹窗标题
        title: {
            type: String,
            default: '租户权限配置'
        },
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false
        },
        // 租户key
        tenantKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dialogSearchParams: {
                mcpName: '',
                toolName: ''
            },
            radioValue: 0,
            radioGroup: [
                { label: '全部', value: 0 },
                { label: '已选', value: 1 }
            ],
            list: [],
            expandList: [],
            // 权限缓存机制
            permissionCache: {
                // 原始数据状态（弹窗打开时的初始状态）
                originalState: new Map(),
                // 用户变更记录（只记录与原始状态不同的项）
                changes: new Map(),
                // 是否已初始化
                initialized: false
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        radioValue() {
            this.fetchList();
        },
        // 监听弹窗显示状态，关闭时重置数据
        visible(newVal, oldVal) {
            if (oldVal && !newVal) {
                // 弹窗从显示变为隐藏时，重置数据
                this.resetComponentData();
            }
        }
    },
    methods: {
        /* ====== 缓存机制 ====== */
        // 初始化缓存
        initCache() {
            this.permissionCache.originalState.clear();
            this.permissionCache.changes.clear();

            // 记录所有权限项的初始状态
            this.list.forEach((mcp) => {
                (mcp.toolList || []).forEach((tool) => {
                    const key = `tool_${tool.toolId}`;
                    this.permissionCache.originalState.set(key, !!tool.isChoice);
                });

                (mcp.resourceList || []).forEach((resource) => {
                    const key = `resource_${resource.resourceId}`;
                    this.permissionCache.originalState.set(key, !!resource.isChoice);
                });
            });

            this.permissionCache.initialized = true;
        },

        // 记录权限变更
        recordChange(type, id, newValue) {
            if (!this.permissionCache.initialized) return;

            const key = `${type}_${id}`;
            const originalValue = this.permissionCache.originalState.get(key);

            // 如果新值与原始值相同，则从变更记录中删除
            if (originalValue === newValue) {
                this.permissionCache.changes.delete(key);
            } else {
                // 否则记录变更
                this.permissionCache.changes.set(key, newValue);
            }
        },

        // 获取权限项的有效状态（原始状态 + 变更）
        getEffectiveState(type, id) {
            const key = `${type}_${id}`;

            // 优先使用变更记录中的状态
            if (this.permissionCache.changes.has(key)) {
                return this.permissionCache.changes.get(key);
            }

            // 否则使用原始状态
            return this.permissionCache.originalState.get(key) || false;
        },

        // 将缓存状态应用到显示数据
        applyChangesToDisplay() {
            if (!this.permissionCache.initialized) return;

            this.list.forEach((mcp) => {
                // 应用工具权限变更
                (mcp.toolList || []).forEach((tool) => {
                    tool.isChoice = this.getEffectiveState('tool', tool.toolId);
                });

                // 应用资源权限变更
                (mcp.resourceList || []).forEach((resource) => {
                    resource.isChoice = this.getEffectiveState('resource', resource.resourceId);
                });

                // 同步到显示列表
                (mcp.displayToolList || []).forEach((tool) => {
                    tool.isChoice = this.getEffectiveState('tool', tool.toolId);
                });

                (mcp.displayResourceList || []).forEach((resource) => {
                    resource.isChoice = this.getEffectiveState('resource', resource.resourceId);
                });

                // 更新选择状态
                mcp.updateSelectAllState_tool();
                mcp.updateSelectAllState_resource();
            });
        },

        // 清理缓存
        clearCache() {
            this.permissionCache.originalState.clear();
            this.permissionCache.changes.clear();
            this.permissionCache.initialized = false;
        },

        /* ====== 公共注入 ====== */
        injectSelectable(mcp) {
            // 保证属性响应式
            this.$set(mcp, 'displayToolList', [...(mcp.toolList || [])]);
            this.$set(mcp, 'displayResourceList', [...(mcp.resourceList || [])]);

            /* ----- 工具相关 ----- */
            // 先定义外层更新函数
            this.$set(mcp, 'isIndeterminate_outer', false);

            mcp.updateOuterChoice = () => {
                const totalInner = mcp.toolList.length + mcp.resourceList.length;
                const selectedCount =
                    mcp.toolList.filter((t) => t.isChoice).length +
                    mcp.resourceList.filter((r) => r.isChoice).length;

                if (selectedCount === 0) {
                    mcp.isChoice = 0;
                    mcp.isIndeterminate_outer = false;
                } else if (selectedCount === totalInner) {
                    mcp.isChoice = 1;
                    mcp.isIndeterminate_outer = false;
                } else {
                    mcp.isChoice = 0;
                    mcp.isIndeterminate_outer = true;
                }
            };
            mcp.updateSelectAllState_tool = () => {
                const total = mcp.displayToolList.length;
                const checked = mcp.displayToolList.filter((i) => i.isChoice).length;
                mcp.checkAll_tool = checked > 0 && checked === total;
                mcp.isIndeterminate_tool = checked > 0 && checked < total;
                mcp.updateOuterChoice();
            };
            mcp.handleCheckAllChange_tool = (val) => {
                mcp.displayToolList.forEach((i) => {
                    i.isChoice = val;
                    // 记录工具权限变更
                    this.recordChange('tool', i.toolId, !!val);
                });
                mcp.updateSelectAllState_tool();
            };
            mcp.handleToolCheckChange = (tool) => {
                // 记录工具权限变更
                this.recordChange('tool', tool.toolId, !!tool.isChoice);
                mcp.updateSelectAllState_tool();
            };

            /* ----- 资源相关 ----- */
            mcp.updateSelectAllState_resource = () => {
                const total = mcp.displayResourceList.length;
                const checked = mcp.displayResourceList.filter((i) => i.isChoice).length;
                mcp.checkAll_resource = checked > 0 && checked === total;
                mcp.isIndeterminate_resource = checked > 0 && checked < total;
                mcp.updateOuterChoice();
            };
            mcp.handleCheckAllChange_resource = (val) => {
                mcp.displayResourceList.forEach((i) => {
                    i.isChoice = val;
                    // 记录资源权限变更
                    this.recordChange('resource', i.resourceId, !!val);
                });
                mcp.updateSelectAllState_resource();
            };
            mcp.handleResourceCheckChange = (resource) => {
                // 记录资源权限变更
                this.recordChange('resource', resource.resourceId, !!resource.isChoice);
                mcp.updateSelectAllState_resource();
            };

            // 计算初始状态
            mcp.updateSelectAllState_tool();
            mcp.updateSelectAllState_resource();

            // ===== 外层复选框逻辑 =====
            mcp.handleCheckAllChange = (val) => {
                const checked = !!val;
                mcp.toolList.forEach((t) => {
                    t.isChoice = checked;
                    // 记录工具权限变更
                    this.recordChange('tool', t.toolId, checked);
                });
                mcp.resourceList.forEach((r) => {
                    r.isChoice = checked;
                    // 记录资源权限变更
                    this.recordChange('resource', r.resourceId, checked);
                });
                // 同步 displayList 中的引用
                mcp.displayToolList.forEach((t) => (t.isChoice = checked));
                mcp.displayResourceList.forEach((r) => (r.isChoice = checked));
                mcp.updateSelectAllState_tool();
                mcp.updateSelectAllState_resource();
                mcp.isIndeterminate_outer = false;
            };

            // 计算初始状态
            mcp.updateSelectAllState_tool();
            mcp.updateSelectAllState_resource();
        },

        /* ====== 列表查询 ====== */
        search() {
            this.fetchList();
        },
        fetchList() {
            Permission.getTenantPermissionConfig({
                tenantKey: this.tenantKey,
                mcpName: this.dialogSearchParams.mcpName,
                toolName: this.dialogSearchParams.toolName,
                isChoice: this.radioValue,
                pageNum: 1,
                pageSize: 999
            }).then((res) => {
                const rawList = res.data.list || [];
                rawList.forEach((mcp) => {
                    mcp.toolList = (mcp.toolList || []).map((t) => ({
                        ...t,
                        isChoice: !!t.isChoice
                    }));
                    mcp.resourceList = (mcp.resourceList || []).map((r) => ({
                        ...r,
                        isChoice: !!r.isChoice
                    }));

                    this.injectSelectable(mcp);
                });
                this.list = rawList;
                this.expandList = [];

                // 如果是首次加载（弹窗打开），初始化缓存
                if (!this.permissionCache.initialized) {
                    this.initCache();
                } else {
                    // 如果是搜索等操作，应用缓存中的变更
                    this.applyChangesToDisplay();
                }
            });
        },

        /* ====== 展开收起 ====== */
        handleOutListExpand(mcpIndex) {
            if (this.expandList.includes(mcpIndex)) {
                this.expandList = [];
            } else {
                this.expandList = [mcpIndex];
            }
        },

        /* ====== 弹窗操作 ====== */
        handleClose() {
            this.clearCache();
            this.resetComponentData();
            this.$emit('on-close');
            this.dialogVisible = false;
        },
        onSuccess() {
            // 直接从当前列表状态获取选择数据
            const configInfos = this.list
                .map((mcp) => {
                    // 从实际的列表状态获取选中的工具和资源
                    const selectedTools = (mcp.toolList || [])
                        .filter((t) => t.isChoice)
                        .map((t) => t.toolId);
                    const selectedResources = (mcp.resourceList || [])
                        .filter((r) => r.isChoice)
                        .map((r) => r.resourceId);

                    let choiceAllFlag = 0;
                    if (
                        selectedTools.length === mcp.toolList.length &&
                        selectedResources.length === mcp.resourceList.length &&
                        mcp.toolList.length > 0 &&
                        mcp.resourceList.length > 0
                    ) {
                        choiceAllFlag = 1;
                    }

                    return {
                        mcpId: mcp.mcpId,
                        choiceAll: choiceAllFlag,
                        toolIds: selectedTools,
                        resourceIds: selectedResources
                    };
                })
                .filter((c) => c.toolIds.length > 0 || c.resourceIds.length > 0);

            const payload = {
                tenantKey: this.tenantKey,
                configInfos
            };

            Permission.saveTenantPermissionConfig(payload).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '保存成功');
                    this.dialogVisible = false;
                    this.$emit('on-success');
                } else {
                    this.$message.error(res.returnMsg || '保存失败');
                }
            });
        },
        onCancel() {
            this.clearCache();
            this.resetComponentData();
            this.$emit('on-cancel');
            this.dialogVisible = false;
        },

        /* ====== 数据重置 ====== */
        resetComponentData() {
            // 重置搜索参数
            this.dialogSearchParams = {
                mcpName: '',
                toolName: ''
            };
            // 重置单选按钮
            this.radioValue = 0;
            // 清空列表数据
            this.list = [];
            // 清空展开状态
            this.expandList = [];
        }
    }
};
</script>

<style lang="less" scoped>
.config-header {
    height: 75px;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 0 24px;
    gap: 16px;
    .el-input {
        height: 32px;
        width: 280px;
        /deep/.el-input__inner {
            height: 100%;
        }
    }
    .header-radio-group {
        margin-left: auto;
        height: 32px;
        /deep/.el-radio-button {
            margin: 0;
            height: 100%;
            &.is-active {
                .el-radio-button__inner {
                    font-weight: 500;
                    color: #1565ff;
                    border-color: #1565ff;
                    background-color: #1565ff1a;
                    border-radius: 4px;
                }
            }
            &__inner {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                line-height: 20px;
                padding: 0 20px;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}
.config-main {
    min-height: 0;
    flex: 1;
    overflow-y: auto;
    background: #f6f8fa;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    &-item {
        width: 100%;
        height: 78px;
        background-color: #ffffff;
        border-radius: 6px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        .item-content-header {
            height: fit-content;
            display: flex;
            align-items: start;
            justify-content: space-between;
            gap: 16px;
            &-left {
                min-width: 0;
                flex: 1;
                height: 100%;
                &.is-choice {
                    background: #ffffff;
                    box-shadow:
                        0px 5px 12px 4px rgba(0, 0, 0, 0.06),
                        0px 3px 4px 0px rgba(0, 0, 0, 0.08),
                        0px 1px 2px -2px rgba(0, 0, 0, 0.1);
                    border-radius: 4px;
                    border: 1px solid rgba(21, 101, 255, 0.3);
                }
                .item-desc {
                    --line-clamp: 1;
                    margin: 0 0 0 24px;
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 20px;
                }
            }
            &-right {
                width: 32px;
                height: 32px;
                background: #f6f8fa;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                .out-list-expand-icon {
                    font-size: 16px;
                    color: rgba(0, 0, 0, 0.65);
                    cursor: pointer;
                    transition: transform 0.3s ease-in-out;
                    &.rotate180 {
                        transform: rotate(-180deg);
                    }
                }
            }
        }
        .item-content-main {
            height: fit-content;
            padding-left: 24px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            .tool-list-section {
                .tool-list {
                    width: 100%;
                    min-height: 0;
                    flex: 1;
                    overflow-y: auto;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    grid-auto-rows: 98px;
                    gap: 16px;
                    .tool-item {
                        width: 100%;
                        height: 100%;
                        padding: 16px;
                        background: #f6f8fa;
                        border-radius: 4px;
                        border: 1px solid transparent;
                        &.is-choice {
                            background: #ffffff;
                            box-shadow:
                                0px 5px 12px 4px rgba(0, 0, 0, 0.06),
                                0px 3px 4px 0px rgba(0, 0, 0, 0.08),
                                0px 1px 2px -2px rgba(0, 0, 0, 0.1);
                            border-color: rgba(21, 101, 255, 0.3);
                        }
                        .tool-desc {
                            --line-clamp: 2;
                            margin: 0 0 0 24px;
                            font-family:
                                PingFangSC,
                                PingFang SC;
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.65);
                            line-height: 20px;
                        }
                    }
                }
            }
            .resource-list-section {
                .resource-list {
                    width: 100%;
                    min-height: 0;
                    flex: 1;
                    overflow-y: auto;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    grid-auto-rows: 98px;
                    gap: 16px;
                    .resource-item {
                        width: 100%;
                        height: 100%;
                        padding: 16px;
                        background: #f6f8fa;
                        border-radius: 4px;
                        border: 1px solid transparent;
                        &.is-choice {
                            background: #ffffff;
                            box-shadow:
                                0px 5px 12px 4px rgba(0, 0, 0, 0.06),
                                0px 3px 4px 0px rgba(0, 0, 0, 0.08),
                                0px 1px 2px -2px rgba(0, 0, 0, 0.1);
                            border-color: rgba(21, 101, 255, 0.3);
                        }
                        .resource-desc {
                            --line-clamp: 2;
                            margin: 0 0 0 24px;
                            font-family:
                                PingFangSC,
                                PingFang SC;
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.65);
                            line-height: 20px;
                        }
                    }
                }
            }
        }
    }
    .is-expand {
        height: fit-content;
    }
}
.config-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #ffffff;
}

/deep/.el-checkbox {
    &__input {
        &.is-focus {
            .el-checkbox__inner {
                border-color: #1565ff;
            }
        }
        &.is-checked {
            .el-checkbox__inner {
                background-color: #1565ff;
                border-color: #1565ff;
            }
            & + .el-checkbox__label {
                color: rgba(0, 0, 0, 0.85);
            }
        }
        &.is-indeterminate {
            .el-checkbox__inner {
                background-color: #1565ff;
                border-color: #1565ff;
            }
        }
    }
    &__label {
        font-family:
            PingFangSC,
            PingFang SC;
        color: rgba(0, 0, 0, 0.85);
    }
    &.out-list {
        .el-checkbox__label {
            font-weight: 600;
            font-size: 16px;
        }
    }
    &.in-list {
        .el-checkbox__label {
            font-weight: 400;
            font-size: 14px;
        }
    }
}
</style>
<style lang="less">
.tenant-permission-config {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 120px);
    .el-dialog__header {
        height: 65px;
        background: #ffffff;
    }
    .el-dialog__body {
        padding: 0;
        min-height: 0;
        flex: 1;
        overflow-y: auto;
        background: #f6f8fa;
        display: flex;
        flex-direction: column;
    }
    .el-dialog__footer {
        height: fit-content;
        background: #ffffff;
    }
}
</style>
