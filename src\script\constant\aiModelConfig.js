/**
 * AI模型配置文件
 * 支持不同AI模型的差异化配置，包括请求头、参数映射、流式支持等
 */
import { reqUtil } from 'mtex-rams-core';
const { tryEncodeParam } = reqUtil;

const encryption = !true;

// 获取平台请求头
const getPlatformHeader = () => {
    const headers = {};
    if (!remoteAjaxUtil.getReplayAttacksHeader) {
        console.warn('平台版本错误，无法获取nonce');
        return headers;
    }
    const frameHeader = remoteAjaxUtil.getReplayAttacksHeader();
    for (let key in frameHeader) {
        const value = frameHeader[key];
        headers[key] = value;
    }
    return headers;
};

/**
 * 通用参数映射函数
 * 将标准聊天参数转换为模型特定格式
 */
export const CommonParamMappers = {
    /** 转换前通用参数格式
    {
        "model": "model-name",               // 模型ID
        "history": [                         // 历史消息数组
            {
                "role": "user",
                "content": "Hello"
            },
            {
                "role": "assistant",
                "content": "Hi there!"
            }
        ],
        "maxTokens": 100,                   // 最大 tokens 数
        "temperature": 0.7,                 // 温度参数
        "stream": true,                     // 是否流式响应
        "query": "Test query",              // 查询参数
        "conversationId": "test-conv-123",  // 对话ID 
        "user": "test-user",                // 用户ID
        "extraParams": {}                    // 其他自定义参数
    }
     */

    /**
     * Default格式映射器
     */
    defaultFormat: (commonParams) => {
        console.log('commonParams', commonParams);

        let param = {
            'webToken': localStorage.getItem('token'),
            'userId': frameService.getUser().id,
            'userName': frameService.getUser().name,
            'userNameCN': frameService.getUser().describe,
            'requestData': {
                'type': 2,
                'message': commonParams.query || '',
                'mcpServiceId': commonParams.extraParams && commonParams.extraParams.mcpServiceId || [],
                'mcpToolId': commonParams.extraParams && commonParams.extraParams.mcpToolId || []
            }
        };
        if (encryption) {
            param = tryEncodeParam(param);
            param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
        }
        return param;
    },

    /**
     * Dify格式映射器
     */
    difyFormat: (commonParams) => {
        let responseMode = 'blocking';
        if (commonParams.stream) {
            responseMode = 'streaming';
        }

        let param = {
            inputs: {},
            query: commonParams.query || '',
            response_mode: responseMode,
            conversation_id: commonParams.conversationId || '',
            user: commonParams.user || 'default-user',
            ...commonParams.extraParams
        };
        if (encryption) {
            param = tryEncodeParam(param);
            param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
        }
        return param;
    },

    /**
     * OpenAI兼容格式映射器
     */
    openaiCompatible: (commonParams) => {
        let param = {
            model: commonParams.model,
            messages: commonParams.history || [],
            temperature: commonParams.temperature || 0.6,
            max_tokens: commonParams.maxTokens,
            stream: commonParams.stream || false,
            ...commonParams.extraParams
        };
        if (encryption) {
            param = tryEncodeParam(param);
            param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
        }
        return param;
    },

};

// 扁平化的AI模型配置
export const AI_MODELS = {
    default: {
        id: 'default',
        name: 'Default',
        description: '默认模型对话',
        provider: '',

        // 网络配置
        gateway: '/mcpservice',
        endpoint: '/ai/v1/chat',
        isStreaming: true,

        // 请求头配置
        headers: {
            ...getPlatformHeader(),
            'Content-Type': 'application/json',
        },

        // 参数映射函数
        params: CommonParamMappers.defaultFormat,

        // 响应解析配置
        responsePaths: {
            streamDataPath: 'content',
            completeDataPath: 'content'
        },

        // 流式响应标识符
        streamIdentifiers: {
            dataPrefix: 'data:',
            doneSignal: '"type":"end"'
        },
    },
    Dify: {
        id: 'Dify',
        name: 'Dify',
        description: '位置资产接入模型',
        provider: 'Dify AI',

        // 网络配置
        gateway: '/dify-service',
        endpoint: '/v1/chat-messages',
        isStreaming: true,

        // 请求头配置
        headers: {
            ...getPlatformHeader(),
            'Content-Type': 'application/json',
            Authorization: 'Bearer app-j6ewRPc3lcNeQkCfyKK1oAvu'
        },

        // 参数映射函数
        params: CommonParamMappers.difyFormat,

        // 响应解析配置
        responsePaths: {
            streamDataPath: 'answer',
            completeDataPath: 'answer'
        },

        // 流式响应标识符
        streamIdentifiers: {
            dataPrefix: 'data:',
            doneSignal: '"event": "message_end"'
        },

        // 模型限制
        maxTokens: 200000,
        contextWindow: 200000,
        defaultTemperature: 0.6,
    },
};

// 默认模型ID
export const DEFAULT_MODEL = 'default';

// 获取模型配置
export const getModelConfig = (modelId = DEFAULT_MODEL) => {
    return AI_MODELS[modelId] || AI_MODELS[DEFAULT_MODEL];
};

// 获取模型列表
export const getModelList = () => {
    return Object.values(AI_MODELS);
};

// 获取默认模型
export const getDefaultModel = () => {
    return DEFAULT_MODEL;
};

// 根据提供商获取模型列表
export const getModelsByProvider = (provider) => {
    return Object.values(AI_MODELS).filter(model => model.provider === provider);
};

// 检查模型是否存在
export const isModelExists = (modelId) => {
    return !!AI_MODELS[modelId];
};

/**
 * 获取模型的请求头配置
 * @param {string} modelId - 模型ID
 * @param {Object} customHeaders - 自定义请求头
 * @returns {Object} 合并后的请求头
 */
export const getModelHeaders = (modelId, customHeaders = {}) => {
    const modelConfig = getModelConfig(modelId);
    return {
        ...modelConfig.headers,
        ...customHeaders
    };
};

/**
 * 获取模型的网关和端点配置
 * @param {string} modelId - 模型ID
 * @returns {Object} 网关和端点配置
 */
export const getModelEndpoint = (modelId) => {
    const modelConfig = getModelConfig(modelId);
    return {
        gateway: modelConfig.gateway,
        endpoint: modelConfig.endpoint
    };
};

/**
 * 检查模型是否支持流式响应
 * @param {string} modelId - 模型ID
 * @returns {boolean} 是否支持流式响应
 */
export const isModelStreamingSupported = (modelId) => {
    const modelConfig = getModelConfig(modelId);
    return modelConfig.isStreaming !== false; // 默认支持流式
};

/**
 * 转换通用参数为模型特定格式
 * @param {string} modelId - 模型ID
 * @param {Object} commonParams - 通用参数
 * @returns {Object} 模型特定格式的参数
 */
export const transformParams = (modelId, commonParams) => {
    const modelConfig = getModelConfig(modelId);

    // 如果模型有自定义参数映射函数，使用它
    if (typeof modelConfig.params === 'function') {
        return modelConfig.params(commonParams);
    }

    // 否则使用默认的 OpenAI 兼容格式
    return CommonParamMappers.openaiCompatible(commonParams);
};

/**
 * 获取模型的完整配置（包含所有扩展字段）
 * @param {string} modelId - 模型ID
 * @returns {Object} 完整的模型配置
 */
export const getExtendedModelConfig = (modelId) => {
    const baseConfig = getModelConfig(modelId);

    return {
        ...baseConfig,
        // 确保所有必需字段都有默认值
        gateway: baseConfig.gateway,
        isStreaming: baseConfig.isStreaming !== false,
        headers: baseConfig.headers,
        params: baseConfig.params
    };
};

// 响应数据提取器
export class ResponseExtractor {
    constructor(modelId = DEFAULT_MODEL) {
        this.config = getModelConfig(modelId);
    }

    // 提取流式响应内容
    extractStreamContent(data) {
        try {
            const path = this.config.responsePaths.streamDataPath;
            return this.getValueByPath(data, path);
        } catch (error) {
            return null;
        }
    }

    // 提取完整响应内容
    extractCompleteContent(data) {
        try {
            const path = this.config.responsePaths.completeDataPath;
            return this.getValueByPath(data, path);
        } catch (error) {
            return null;
        }
    }

    // 提取错误信息（简化版 - 使用通用错误处理）
    extractError(data) {
        try {
            // 尝试常见的错误路径
            const errorMessage =
                this.getValueByPath(data, 'error.message') ||
                this.getValueByPath(data, 'error') ||
                this.getValueByPath(data, 'message') ||
                '未知错误';

            const errorCode =
                this.getValueByPath(data, 'error.code') ||
                this.getValueByPath(data, 'code') ||
                null;

            return {
                message: errorMessage,
                code: errorCode,
                type: 'api_error'
            };
        } catch (error) {
            return { message: '未知错误' };
        }
    }

    // 检查是否为流式结束标识
    isStreamDone(data) {
        const doneSignal = this.config.streamIdentifiers.doneSignal;
        return data.includes(doneSignal);
    }

    // 根据路径提取值
    getValueByPath(obj, path) {
        if (!path || !obj) return null;

        // 支持数组索引和对象属性访问
        const keys = path.replace(/\[(\d+)\]/g, '.$1').split('.');
        let result = obj;

        for (const key of keys) {
            if (result === null || result === undefined) return null;
            result = result[key];
        }

        return result;
    }
}

// 导出默认配置
export default {
    models: AI_MODELS,
    defaultModel: DEFAULT_MODEL,
    getModelConfig,
    getModelList,
    getDefaultModel,
    getModelsByProvider,
    isModelExists,
    ResponseExtractor
};
