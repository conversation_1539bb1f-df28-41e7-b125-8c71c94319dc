/**
 * 统一的数据模型定义
 * 确保整个应用使用一致的数据结构
 */

/**
 * 消息数据模型
 */
export const MessageModel = {
    /**
     * 创建标准消息对象
     * @param {Object} data - 消息数据
     * @returns {Object} 标准化的消息对象
     */
    create(data = {}) {
        return {
            id: data.id || this.generateId(),
            role: data.role || 'user', // 'user' | 'assistant' | 'system'
            content: data.content || '',
            timestamp: data.timestamp || Date.now(),
            sessionId: data.sessionId || null,
            model: data.model || null,
            isError: data.isError || false,
            metadata: data.metadata || {}
        };
    },

    /**
     * 验证消息对象
     * @param {Object} message - 消息对象
     * @returns {boolean} 是否有效
     */
    validate(message) {
        return (
            message &&
            typeof message.id === 'string' &&
            typeof message.role === 'string' &&
            typeof message.content === 'string' &&
            typeof message.timestamp === 'number'
        );
    },

    /**
     * 生成消息ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'msg_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
    },

    /**
     * 转换为显示格式（兼容旧组件）
     * @param {Object} message - 标准消息对象
     * @returns {Object} 显示格式的消息对象
     */
    toDisplayFormat(message) {
        return {
            ...message,
            type: message.role, // 兼容字段
            text: message.content // 兼容字段
        };
    },

    /**
     * 从显示格式转换（兼容旧组件）
     * @param {Object} displayMessage - 显示格式的消息对象
     * @returns {Object} 标准消息对象
     */
    fromDisplayFormat(displayMessage) {
        return {
            id: displayMessage.id,
            role: displayMessage.role || displayMessage.type,
            content: displayMessage.content || displayMessage.text,
            timestamp: displayMessage.timestamp,
            sessionId: displayMessage.sessionId,
            model: displayMessage.model,
            isError: displayMessage.isError || false,
            metadata: displayMessage.metadata || {}
        };
    }
};

/**
 * 会话数据模型
 */
export const SessionModel = {
    /**
     * 创建标准会话对象
     * @param {Object} data - 会话数据
     * @returns {Object} 标准化的会话对象
     */
    create(data = {}) {
        return {
            id: data.id || this.generateId(),
            title: data.title || '新对话',
            createdAt: data.createdAt || Date.now(),
            updatedAt: data.updatedAt || Date.now(),
            model: data.model || null,
            messageCount: data.messageCount || 0,
            messages: data.messages || [],
            metadata: data.metadata || {}
        };
    },

    /**
     * 验证会话对象
     * @param {Object} session - 会话对象
     * @returns {boolean} 是否有效
     */
    validate(session) {
        return (
            session &&
            typeof session.id === 'string' &&
            typeof session.title === 'string' &&
            typeof session.createdAt === 'number' &&
            typeof session.updatedAt === 'number' &&
            Array.isArray(session.messages)
        );
    },

    /**
     * 生成会话ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'session_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
    },

    /**
     * 生成临时会话ID
     * @returns {string} 临时ID
     */
    generateTempId() {
        return 'temp_' + Date.now().toString(36) + '_' + Math.random().toString(36).substring(2);
    },

    /**
     * 检查是否为临时会话
     * @param {string} sessionId - 会话ID
     * @returns {boolean} 是否为临时会话
     */
    isTemporary(sessionId) {
        return sessionId && sessionId.startsWith('temp_');
    },

    /**
     * 更新会话时间戳
     * @param {Object} session - 会话对象
     * @returns {Object} 更新后的会话对象
     */
    touch(session) {
        return {
            ...session,
            updatedAt: Date.now()
        };
    }
};

/**
 * 菜单项数据模型（用于侧边栏）
 */
export const MenuItemModel = {
    /**
     * 创建菜单项
     * @param {Object} session - 会话对象
     * @returns {Object} 菜单项对象
     */
    fromSession(session) {
        return {
            id: session.id,
            text: session.title,
            type: 'session',
            timestamp: session.updatedAt,
            sessionId: session.id
        };
    },

    /**
     * 创建菜单组
     * @param {string} key - 组键
     * @param {string} title - 组标题
     * @param {Array} items - 菜单项数组
     * @returns {Object} 菜单组对象
     */
    createGroup(key, title, items = []) {
        return {
            key,
            title,
            items
        };
    }
};

/**
 * 模型选项数据模型
 */
export const ModelOptionModel = {
    /**
     * 从AI模型配置创建选项
     * @param {Object} modelConfig - AI模型配置
     * @returns {Object} 模型选项对象
     */
    fromConfig(modelConfig) {
        return {
            value: modelConfig.id,
            label: modelConfig.name,
            desc: modelConfig.description,
            provider: modelConfig.provider
        };
    }
};

/**
 * 数据转换工具
 */
export const DataTransformer = {
    /**
     * 批量转换消息为显示格式
     * @param {Array} messages - 标准消息数组
     * @returns {Array} 显示格式消息数组
     */
    messagesToDisplayFormat(messages) {
        return messages.map(msg => MessageModel.toDisplayFormat(msg));
    },

    /**
     * 批量转换消息为标准格式
     * @param {Array} displayMessages - 显示格式消息数组
     * @returns {Array} 标准消息数组
     */
    messagesFromDisplayFormat(displayMessages) {
        return displayMessages.map(msg => MessageModel.fromDisplayFormat(msg));
    },

    /**
     * 会话列表转换为菜单组
     * @param {Array} sessions - 会话数组
     * @returns {Array} 菜单组数组
     */
    sessionsToMenuGroups(sessions) {
        const today = new Date();
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        const groups = {
            today: MenuItemModel.createGroup('today', '今天', []),
            yesterday: MenuItemModel.createGroup('yesterday', '昨天', []),
            week: MenuItemModel.createGroup('week', '最近7天', []),
            older: MenuItemModel.createGroup('older', '更早', [])
        };

        sessions.forEach(session => {
            const sessionDate = new Date(session.updatedAt);
            const menuItem = MenuItemModel.fromSession(session);

            if (this.isSameDay(sessionDate, today)) {
                groups.today.items.push(menuItem);
            } else if (this.isSameDay(sessionDate, yesterday)) {
                groups.yesterday.items.push(menuItem);
            } else if (sessionDate > weekAgo) {
                groups.week.items.push(menuItem);
            } else {
                groups.older.items.push(menuItem);
            }
        });

        // 过滤空组并排序
        return Object.values(groups)
            .filter(group => group.items.length > 0)
            .map(group => ({
                ...group,
                items: group.items.sort((a, b) => b.timestamp - a.timestamp)
            }));
    },

    /**
     * 判断是否为同一天
     * @param {Date} date1 - 日期1
     * @param {Date} date2 - 日期2
     * @returns {boolean} 是否为同一天
     */
    isSameDay(date1, date2) {
        return (
            date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate()
        );
    }
};

// 导出默认配置
export default {
    MessageModel,
    SessionModel,
    MenuItemModel,
    ModelOptionModel,
    DataTransformer
};
