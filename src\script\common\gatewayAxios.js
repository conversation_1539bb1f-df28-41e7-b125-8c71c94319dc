import axios from 'axios';
import { reqUtil } from 'mtex-rams-core';
const { decodeResult, tryEncodeParam } = reqUtil;
const POST_SUCCESS = '000001';
const GateWay = '/mcpservice';
const URL_UploadFile = 'upload_file/';
const timeout = 300 * 1000; //   超时时限
const getTenantUrl = 'usermanage/getTenantInfo'; //获取用户租户信息url
const TenantUrlGateWay = '/locservice/';



const CancelToken = axios.CancelToken;
const getUploadFileBasePath = (src) => {
    if (!src) {
        return `${GateWay}${URL_UploadFile}`;
    }
    src = encodeURI(src);
    return `${GateWay}${URL_UploadFile}${src}`;
};

const clearHttpRequestList = () => {
    if (window.$httpRequestList.length > 0) {
        window.$httpRequestList.forEach((item) => {
            item();
        });
        window.$httpRequestList = [];
    }
};
const encryption = true;
export { POST_SUCCESS, GateWay, getUploadFileBasePath, clearHttpRequestList };

axios.interceptors.request.use((config) => {
    if (!remoteAjaxUtil.getReplayAttacksHeader) {
        console.warn('平台版本错误，无法获取nonce');
        return config;
    }
    const frameHeader = remoteAjaxUtil.getReplayAttacksHeader();
    for (let key in frameHeader) {
        const value = frameHeader[key];
        config.headers[key] = value;
    }
    return config;
});
/*
    初始化
*/

export default class {
    constructor({ operateUserId, sourceSystemId, sourceSystemName, isTenant, gateWay }) {
        this.gateway = gateWay || GateWay; //可配置不同gateWay
        this.webToken = localStorage.getItem('token'); // '8B7A5A535270024DB0904D704B633181' ||
        this.operateUserId = operateUserId; //操作人id
        this.sourceSystemId = sourceSystemId; //服务消费方id
        this.sourceSystemName = sourceSystemName; //服务消费方名称
        this.userName = frameService.getUser().name;
        this.userNameCN = frameService.getUser().describe;
        this.userId = frameService.getUser().id;
        this.isTenant = isTenant;
    }
    update(umName) {
        let param = {
            operateUserId: this.operateUserId,
            webToken: this.webToken,
            userName: this.userName,
            userNameCN: this.userNameCN,
            userId: this.userId,
            requestData: {
                umName: umName
            }
        };
        param = tryEncodeParam(param);
        param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
        return new Promise((resolve, reject) => {
            axios
                .post(`${TenantUrlGateWay}${getTenantUrl}`, param, { timeout: timeout })
                .then((res) => {
                    let newRes = res.data;
                    if (newRes && newRes.encodeResp) {
                        newRes = decodeResult(res.data);
                    }
                    let result = newRes;
                    resolve(result);
                    return;
                })
                .catch((err) => {
                    let newRes = [];
                    if (err.response && err.response.data) {
                        newRes = err.response.data;
                        if (newRes && newRes.encodeResp) {
                            // 解密无endcode 无法解密
                            if (!newRes.encodeResp.endsWith('ENDCODE')) {
                                newRes.encodeResp = newRes.encodeResp + 'ENDCODE';
                            }
                            newRes = decodeResult(newRes);
                        }
                    }
                    let result = newRes;
                    //避免因为报错整个页面都无法加载。
                    resolve(result);
                });
        });
    }
    async post(url, param) {
        /*
            入参通用格式：
            {
                "operateUserId":"220551014393053187", //操作人id
                "sourceSystemId":"FE2BF9D29678468CB56F78BFA8B99294", //服务消费方id
                "sourceSystemName":"德院", //服务消费方名称
                ...
            }
        */
        param = param || {};
        if (param instanceof FormData) {
            param.append('operateUserId', this.operateUserId);
            param.append('webToken', this.webToken);
            param.append('sourceSystemId', this.sourceSystemId);
            param.append('sourceSystemName', this.sourceSystemName);
            param.append('userName', this.userName);
            param.append('userNameCN', this.userNameCN);
            param.append('userId', frameService.getUser().id);
        } else {
            param.operateUserId = param.operateUserId || this.operateUserId;
            param.webToken = param.webToken || this.webToken;
            param.sourceSystemId = param.sourceSystemId || this.sourceSystemId;
            param.sourceSystemName = param.sourceSystemName || this.sourceSystemName;
            param.userName = param.userName || this.userName;
            param.userNameCN = param.userNameCN || this.userNameCN;
            param.userId = frameService.getUser().id;
            if (url === 'usermanage/addTenantInfo') {
                param.operateUserId = '220551014393053187';
                param.sourceSystemName = 'Web配置';
            }
            if (encryption) {
                param = tryEncodeParam(param);
                param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
            }
        }
        /*
            返回格式：
            {
                "returnCode": "000001", //业务服务执行标识
                "returnMsg": "接口调用成功", //业务服务返回代码
                "serviceFlag": "TRUE", //业务服务返回消息
                "data": null //成功返回实体信息
            }
        */

        return new Promise((resolve, reject) => {
            let rootPath = this.gateway;
            if (this.isTenant) {
                rootPath = TenantUrlGateWay;
                delete param.sourceSystemName; //旧的接口不添加sourceSystemName字段
            }
            axios
                .post(`${rootPath}${url}`, param, {
                    timeout: timeout,
                    cancelToken: new CancelToken((c) => window.$httpRequestList.push(c))
                })
                .then((res) => {
                    let newRes = res.data;
                    if (encryption) {
                        if (newRes && newRes.encodeResp) {
                            newRes = decodeResult(res.data);
                        }
                    }
                    let result = newRes;
                    if (
                        res.headers &&
                        res.headers['content-type'] === 'application/force-download'
                    ) {
                        resolve(result);
                        return;
                    }
                    // 不需要拦截，直接返回
                    // if (result.returnCode == POST_SUCCESS) {
                    resolve(result);
                    return;
                    // }
                    // reject({
                    //     success: result.returnCode,
                    //     errorMessage: result.returnMsg
                    // });
                    // return;
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        return;
                    }
                    reject({
                        success: 111,
                        errorMessage: err.message
                    });
                });
        });
    }
    get(url, param) {
        let _param = {
            operateUserId: param.operateUserId || this.operateUserId,
            webToken: param.webToken || this.webToken,
            sourceSystemId: param.sourceSystemId || this.sourceSystemId,
            sourceSystemName: param.sourceSystemName || this.sourceSystemName,
            userName: param.userName || this.userName,
            userNameCN: param.userNameCN || this.userNameCN,
            userId: frameService.getUser().id
        };
        if (encryption) {
            _param = tryEncodeParam(_param);
            _param.webEncodeParam = _param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
        }
        return new Promise((resolve, reject) => {
            let rootPath = this.gateway;
            if (this.isTenant) {
                rootPath = TenantUrlGateWay;
                delete _param.sourceSystemName;
            }
            axios
                .get(`${rootPath}${url}`, { params: _param })
                .then((res) => {
                    let newRes = res.data;
                    if (encryption) {
                        if (newRes && newRes.encodeResp) {
                            newRes = decodeResult(res.data);
                        }
                    }
                    let result = newRes;
                    if (result.returnCode == POST_SUCCESS) {
                        resolve(result);
                        return;
                    }
                    reject({
                        success: result.returnCode,
                        errorMessage: result.returnMsg
                    });
                    return;
                })
                .catch((err) => {
                    reject({
                        success: 111,
                        errorMessage: err.message
                    });
                });
        });
    }
    getFile(url, param, options = {}) {
        param = param || {};
        if (param instanceof FormData) {
            param.append('operateUserId', this.operateUserId);
            param.append('webToken', this.webToken);
            param.append('sourceSystemId', this.sourceSystemId);
            param.append('sourceSystemName', this.sourceSystemName);
            param.append('userName', this.userName);
            param.append('userNameCN', this.userNameCN);
            param.append('userId', this.userId);
        } else {
            param.operateUserId = param.operateUserId || this.operateUserId;
            param.webToken = param.webToken || this.webToken;
            param.sourceSystemId = param.sourceSystemId || this.sourceSystemId;
            param.sourceSystemName = param.sourceSystemName || this.sourceSystemName;
            param.userName = param.userName || this.userName;
            param.userNameCN = param.userNameCN || this.userNameCN;
            param.userId = param.userId || this.userId;
            if (encryption) {
                param = tryEncodeParam(param);
                param.webEncodeParam = param.webEncodeParam.match(/(\S*)ENDCODE/)[1];
            }
            // param = tryEncodeParam(param);
        }
        return new Promise((resolve, reject) => {
            let rootPath = this.gateway;
            if (this.isTenant) {
                rootPath = TenantUrlGateWay;
                delete param.sourceSystemName;
            }
            axios
                .post(`${rootPath}${url}`, param, { timeout: timeout, ...options })
                .then((res) => {
                    // let result = res.data;
                    if (res.status === 200) {
                        resolve(res);
                        return;
                    }
                    reject(res);
                    return;
                })
                .catch((err) => {
                    reject({
                        success: 111,
                        errorMessage: err.message
                    });
                });
        });
    }
}
