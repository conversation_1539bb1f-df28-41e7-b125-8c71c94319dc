<template>
    <div class="message-input">
        <div class="input-container">
            <textarea
                class="chat-input custom-scrollbar"
                v-model="inputText"
                :placeholder="
                    shouldShowStopButton ? '正在生成回复，可以输入下一个问题...' : placeholder
                "
                :disabled="isInputDisabled"
                resize="none"
                rows="2"
                @keydown="handleKeydown"
            ></textarea>
            <div class="input-actions">
                <el-select
                    v-model="currentModel"
                    size="small"
                    class="model-select"
                    :popper-class="popperClass"
                    @change="handleModelChange"
                >
                    <el-option
                        v-for="model in modelOptions"
                        :key="model.value"
                        :label="model.label"
                        :value="model.value"
                    >
                        <div class="model-option" v-if="showModelDetails">
                            <div class="model-option-text">
                                <div class="model-name">{{ model.label }}</div>
                                <div class="model-desc">{{ model.desc }}</div>
                            </div>
                            <i
                                class="el-icon-check model-selected"
                                v-if="currentModel === model.value"
                            ></i>
                        </div>
                        <span v-else>{{ model.label }}</span>
                    </el-option>
                </el-select>
                <el-popover
                    placement="top"
                    width="240"
                    trigger="click"
                    popper-class="mcp-popover mcpservice-theme"
                    v-model="mcpPopoverVisible"
                >
                    <div class="mcp-popover-content">
                        <!-- 搜索框 -->
                        <div class="mcp-search-box">
                            <el-input
                                v-model="mcpSearchText"
                                placeholder="按服务名称检索"
                                size="small"
                                prefix-icon="el-icon-search"
                                clearable
                                @input="handleMcpSearch"
                                @clear="handleMcpSearch"
                            >
                            </el-input>
                        </div>

                        <!-- MCP服务树形列表 -->
                        <div
                            class="mcp-service-tree custom-scrollbar"
                            ref="mcpTreeContainer"
                            @scroll="handleTreeScroll"
                        >
                            <!-- 加载状态 -->
                            <div
                                v-if="mcpLoading && mcpServices.length === 0"
                                class="loading-container"
                            >
                                <i class="el-icon-loading"></i>
                                <span>加载中...</span>
                            </div>

                            <!-- 错误状态 -->
                            <div
                                v-else-if="mcpError && mcpServices.length === 0"
                                class="error-container"
                            >
                                <i class="el-icon-warning"></i>
                                <span>{{ mcpError }}</span>
                                <el-button size="mini" @click="loadMcpServices(true)"
                                    >重试</el-button
                                >
                            </div>

                            <!-- 树形组件 -->
                            <el-tree
                                v-else
                                ref="mcpTree"
                                :data="mcpServices"
                                :props="treeProps"
                                show-checkbox
                                node-key="id"
                                :default-expand-all="false"
                                :check-strictly="false"
                                :lazy="true"
                                :load="lazyLoadNode"
                                :filter-node-method="filterNode"
                                @check-change="handleTreeCheckChange"
                            >
                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                    <span class="node-label text-ellipsis" :title="node.label">{{
                                        node.label
                                    }}</span>
                                    <span
                                        v-if="data.description"
                                        class="node-description text-ellipsis"
                                        :title="data.description"
                                    >
                                        {{ data.description }}
                                    </span>
                                </span>
                            </el-tree>

                            <!-- 没有更多数据提示 -->
                            <div v-if="!hasMoreData && mcpServices.length > 0" class="no-more-data">
                                没有更多数据了
                            </div>
                            <!-- 加载更多指示器 -->
                            <div v-else class="loading-more">
                                <i class="el-icon-loading"></i>
                                <span>加载更多...</span>
                            </div>
                        </div>
                    </div>
                    <div slot="reference" class="mcp-select">
                        <span>选择MCP服务</span>
                        <span v-if="selectedServicesCount > 0" class="selected-count">
                            ({{ selectedServicesCount }})
                        </span>
                    </div>
                </el-popover>
                <div class="right-actions">
                    <!-- 语音 -->
                    <!-- <el-tooltip
                        v-if="showMicButton"
                        class="item"
                        effect="dark"
                        content="语音输入"
                        placement="top"
                        popper-class="mic-popper"
                    >
                        <div class="mic-btn" @click="handleMicClick">
                            <i class="el-icon-microphone"></i>
                        </div>
                    </el-tooltip> -->
                    <!-- 发送/停止按钮 -->
                    <div
                        class="send-btn"
                        :class="{
                            'is-disabled':
                                !shouldShowStopButton && (!inputText.trim() || isSendDisabled),
                            'is-stop': shouldShowStopButton
                        }"
                        @click="handleSendOrStop"
                    >
                        <i :class="shouldShowStopButton ? 'el-icon-close' : 'el-icon-top'"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import ConfigService from '@/script/api/module/config-service';

export default {
    name: 'MessageInput',
    props: {
        // 输入框配置
        placeholder: {
            type: String,
            default: '请输入您的问题，Shift+Enter可换行，Enter发送'
        },
        // 模型选项
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        },

        // 功能开关
        showMicButton: {
            type: Boolean,
            default: false
        },
        showModelDetails: {
            type: Boolean,
            default: false
        },

        // 其他配置
        popperClass: {
            type: String,
            default: 'model-select-popper'
        },
        sendButtonTooltip: {
            type: String,
            default: '请输入你的问题'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        // AI响应状态
        isAiResponding: {
            type: Boolean,
            default: false
        },
        // 流式响应状态
        isStreamResponse: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            inputText: '',
            currentModel: this.selectedModel || this.defaultModel,

            // MCP服务相关数据
            mcpPopoverVisible: false,
            mcpSearchText: '',
            treeProps: {
                children: 'children',
                label: 'label',
                disabled: 'disabled',
                isLeaf: 'leaf'
            },
            selectedServicesCount: 0,
            mcpServices: [], // 改为空数组，通过API动态加载

            // API相关状态
            lastScrollTop: 0,
            mcpLoading: false,
            mcpError: null,
            currentPageSize: 10, // 当前页面大小
            hasMoreData: true, // 是否还有更多数据
            loadingMore: false, // 是否正在加载更多
            searchDebounceTimer: null, // 搜索防抖定时器
            expandedNodeCache: new Map(), // 已展开节点的缓存
            selectedNodeCache: new Set(), // 选中节点的缓存
            checkedKeysCache: [], // 选中节点ID的缓存
            isUpdatingTreeState: false // 防止递归更新树状态的标志
        };
    },

    created() {
        // 确保禁用状态正确继承
        this.processDisabledState();
    },
    mounted() {
        // 初始化加载MCP服务列表
        this.loadMcpServices();
    },
    beforeDestroy() {
        // 清理定时器
        if (this.searchDebounceTimer) {
            clearTimeout(this.searchDebounceTimer);
        }
        // 清理缓存
        this.clearExpandCache();
        this.clearCheckedCache();
    },
    computed: {
        // 是否应该显示停止按钮
        shouldShowStopButton() {
            return this.isAiResponding || this.isStreamResponse;
        },
        // 是否应该禁用输入框（只有在disabled prop为true时才禁用）
        isInputDisabled() {
            return this.disabled;
        },
        // 是否应该禁用发送（在AI响应时禁用发送但允许输入）
        isSendDisabled() {
            return this.disabled || this.shouldShowStopButton;
        },
        // 过滤后的MCP服务列表
        filteredMcpServices() {
            return this.mcpServices;
        }
    },
    methods: {
        // ==================== MCP API相关方法 ====================

        // 加载MCP服务列表
        async loadMcpServices(reset = false) {
            if (this.mcpLoading) return;

            try {
                this.mcpLoading = true;
                this.mcpError = null;

                // 保存当前的选中状态（在重置或加载更多之前）
                let currentCheckedKeys = [];
                if (this.$refs.mcpTree && !reset) {
                    currentCheckedKeys = this.$refs.mcpTree.getCheckedKeys();
                }

                // 如果是重置，清空现有数据
                if (reset) {
                    this.mcpServices = [];
                    this.currentPageSize = 10;
                    this.hasMoreData = true;
                    // 清除展开缓存
                    this.clearExpandCache();
                    // 注意：搜索时不清除选中状态缓存，保持用户选择
                    // this.clearCheckedCache(); // 移除这行，保持缓存
                }

                const response = await ConfigService.getMCPInfoPage({
                    pageNum: 1,
                    pageSize: this.currentPageSize,
                    mcpDescription: '',
                    mcpName: '', // 总是加载所有数据，通过前端过滤
                    isValid: 1
                });

                if (response.serviceFlag === 'TRUE') {
                    const newServices = this.transformMcpData(response.data.list || []);

                    if (reset) {
                        // 重置时直接替换
                        this.mcpServices = newServices;
                    } else {
                        // 加载更多时，合并数据避免重复
                        const existingIds = new Set(this.mcpServices.map((s) => s.id));
                        const uniqueNewServices = newServices.filter((s) => !existingIds.has(s.id));
                        this.mcpServices.push(...uniqueNewServices);
                    }

                    // 检查是否还有更多数据
                    this.hasMoreData = response.data.list.length === this.currentPageSize;

                    // 恢复选中状态
                    this.$nextTick(() => {
                        if (reset) {
                            // 搜索重置时，恢复之前缓存的状态
                            this.restoreCheckedState();
                        } else {
                            // 加载更多时，恢复当前的选中状态
                            if (currentCheckedKeys.length > 0) {
                                this.restoreSpecificCheckedState(currentCheckedKeys);
                            }
                        }
                    });
                } else {
                    this.mcpError = response.returnMsg || '获取MCP服务列表失败';
                    this.$message.error(this.mcpError);
                }
            } catch (error) {
                console.error('加载MCP服务失败:', error);
                this.mcpError = '网络错误，请稍后重试';
                this.$message.error(this.mcpError);
            } finally {
                this.mcpLoading = false;
                this.loadingMore = false;
            }
        },

        // 转换API数据为树形结构
        transformMcpData(apiData) {
            return apiData.map((item) => ({
                id: item.mcpId,
                label: item.mcpName,
                disabled: Boolean(!item.isValid),
                leaf: false, // 默认非叶子节点
                isTool: false,
                rawData: item // 保存原始数据
            }));
        },

        // 加载更多数据（滚动到底部时调用）
        loadMoreMcpServices: _.debounce(async function () {
            if (!this.hasMoreData || this.loadingMore || this.mcpLoading) return;

            this.loadingMore = true;
            this.currentPageSize += 10;
            await this.loadMcpServices(false);
        }, 300),

        // 懒加载节点
        async lazyLoadNode(node, resolve) {
            if (node.level === 2) {
                return;
            }
            if (node.level === 0) {
                resolve(this.mcpServices);
                return;
            }
            const mcpId = node.data.id;

            // 只处理服务节点的展开，工具节点不需要懒加载
            if (node.data.leaf) {
                resolve([]);
                return;
            }

            // 检查缓存
            if (this.expandedNodeCache.has(mcpId)) {
                const cachedData = this.expandedNodeCache.get(mcpId);
                resolve(cachedData);
                return;
            }

            try {
                // 显示加载状态（可以通过设置节点loading状态）
                node.loading = true;

                const response = await ConfigService.getMCPDetails({ mcpId });

                if (response.serviceFlag === 'TRUE') {
                    const tools = this.transformToolsData(response.data);

                    // 缓存结果
                    this.expandedNodeCache.set(mcpId, tools);

                    // 如果没有工具，动态设置为叶子节点
                    if (tools.length === 0) {
                        node.data.leaf = true;
                    }

                    resolve(tools);
                }
            } catch (error) {
                console.error('获取MCP服务详情失败:', error);
            } finally {
                // 清除加载状态
                node.loading = false;
            }
        },

        // 转换工具数据
        transformToolsData(detailData) {
            // 根据API返回的数据结构解析工具列表
            const tools = [];

            if (!detailData || !detailData.toolList) {
                return tools;
            }

            try {
                let toolList = detailData.toolList;

                // 检查工具列表
                if (toolList && Array.isArray(toolList)) {
                    toolList.forEach((tool) => {
                        if (tool.isValid) {
                            tools.push({
                                id: tool.toolId,
                                label: tool.toolName,
                                description: tool.toolDescription || '',
                                disabled: Boolean(!tool.isValid),
                                isValid: tool.isValid,
                                leaf: true, // 默认叶子节点
                                isTool: true,
                                toolData: tool // 保存完整的工具数据
                            });
                        }
                    });
                }
            } catch (error) {
                console.error('解析工具配置失败:', error);
            }

            return tools;
        },

        // 搜索防抖处理
        handleMcpSearch() {
            if (this.searchDebounceTimer) {
                clearTimeout(this.searchDebounceTimer);
            }

            this.searchDebounceTimer = setTimeout(() => {
                // 使用树形组件的内置过滤功能，而不是重新加载数据
                if (this.$refs.mcpTree) {
                    this.$refs.mcpTree.filter(this.mcpSearchText.trim());
                }

                // 如果搜索框为空且当前没有数据，则加载数据
                if (!this.mcpSearchText.trim() && this.mcpServices.length === 0) {
                    this.loadMcpServices(true);
                }
            }, 300); // 300ms防抖
        },

        // 清除展开缓存（在搜索重置时调用）
        clearExpandCache() {
            this.expandedNodeCache.clear();
        },

        // 清除选中状态缓存
        clearCheckedCache() {
            this.selectedNodeCache.clear();
            this.checkedKeysCache = [];
        },

        // 树形组件的节点过滤方法
        filterNode(value, data, node) {
            if (!value) return true;

            const searchText = value.toLowerCase();
            const nodeLabel = (data.label || '').toLowerCase();
            const nodeDescription = (data.description || '').toLowerCase();

            // 当前节点匹配
            const currentNodeMatches =
                nodeLabel.includes(searchText) || nodeDescription.includes(searchText);

            // 如果是工具节点（子节点）
            if (data.isTool) {
                // 工具节点匹配，或者父服务节点匹配
                const parentNode = node.parent;
                if (parentNode && parentNode.data) {
                    const parentLabel = (parentNode.data.label || '').toLowerCase();
                    const parentDescription = (parentNode.data.description || '').toLowerCase();
                    const parentMatches =
                        parentLabel.includes(searchText) || parentDescription.includes(searchText);

                    return currentNodeMatches || parentMatches;
                }
                return currentNodeMatches;
            }
            // 如果是服务节点（父节点）
            if (currentNodeMatches) {
                return true;
            }

            // 检查是否有子工具节点匹配
            if (node.childNodes && node.childNodes.length > 0) {
                return node.childNodes.some((childNode) => {
                    if (childNode.data && childNode.data.isTool) {
                        const childLabel = (childNode.data.label || '').toLowerCase();
                        const childDescription = (childNode.data.description || '').toLowerCase();
                        return (
                            childLabel.includes(searchText) || childDescription.includes(searchText)
                        );
                    }
                    return false;
                });
            }

            return false;
        },

        // ==================== 原有方法 ====================

        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                // Enter键发送消息或停止
                event.preventDefault();
                this.handleSendOrStop();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim() && !this.isSendDisabled) {
                // 获取选中的MCP服务和工具
                const { mcpServiceId, mcpToolId } = this.getSelectedData();
                console.group('SELECTED_DATA___________');
                console.log('mcpServiceId', mcpServiceId);
                console.log('mcpToolId', mcpToolId);
                console.groupEnd();

                const message = {
                    text: this.inputText.trim(),
                    model: this.currentModel,
                    timestamp: new Date().toISOString(),
                    // 添加MCP服务参数到extraParams中
                    extraParams: {
                        mcpServiceId,
                        mcpToolId
                    }
                };

                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        },

        // 获取选中的数据
        getSelectedData() {
            if (!this.$refs.mcpTree) return [];

            const checkedNodes = this.$refs.mcpTree.getCheckedNodes(false, true);

            const result = {
                mcpServiceId: [],
                mcpToolId: []
            };

            checkedNodes.forEach((node) => {
                if (node.isTool) {
                    result.mcpToolId.push(node.id);
                } else {
                    result.mcpServiceId.push(node.id);
                }
            });

            return result;
        },

        // 处理发送或停止按钮点击
        handleSendOrStop() {
            if (this.shouldShowStopButton) {
                // 如果是停止状态，发送停止事件
                this.$emit('stop-generation');
            } else {
                // 否则发送消息
                this.sendMessage();
            }
        },

        // 处理语音输入点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 设置输入文本（外部调用）
        setInputText(text) {
            this.inputText = text;
        },

        // 获取输入文本
        getInputText() {
            return this.inputText;
        },

        // 清空输入框
        clearInput() {
            this.inputText = '';
        },

        // 聚焦输入框
        focus() {
            this.$nextTick(() => {
                const input = this.$el.querySelector('.chat-input');
                if (input) {
                    input.focus();
                }
            });
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.currentModel = newModel;
            this.$emit('model-change', newModel);
        },

        // 处理禁用状态继承
        processDisabledState() {
            this.mcpServices.forEach((service) => {
                if (service.disabled && service.children) {
                    // 如果服务被禁用，禁用所有子工具
                    service.children.forEach((tool) => {
                        tool.disabled = true;
                    });
                }
            });
        },

        // 处理树形列表滚动（用于加载更多数据）
        handleTreeScroll(event) {
            const { target } = event;
            const { scrollTop, scrollHeight, clientHeight } = target;

            if (scrollTop < this.lastScrollTop) {
                // 向上滚动
                this.lastScrollTop = scrollTop;
                return;
            }
            this.lastScrollTop = scrollTop;

            // 当滚动到底部附近时加载更多数据
            if (scrollHeight - scrollTop - clientHeight < 50) {
                scrollHeight - scrollTop - clientHeight < 10 && (this.hasMoreData = true);
                this.loadMoreMcpServices();
            }
        },

        // 处理树节点选中状态变化
        async handleTreeCheckChange(data, isChecked) {
            // 防止递归调用
            if (this.isUpdatingTreeState) return;

            const node = this.$refs.mcpTree.getNode(data);
            if (!node) return;

            // 先更新缓存，保存当前状态
            this.updateCheckedCache();

            try {
                this.isUpdatingTreeState = true;

                // 如果是服务节点的操作
                if (!data.isTool) {
                    await this.handleServiceNodeChange(node, data, isChecked);
                } else {
                    // 如果是工具节点的操作
                    this.handleToolNodeChange(node);
                }

                // 最后更新缓存和计数，确保所有状态变化完成
                this.$nextTick(() => {
                    this.updateCheckedCache();
                    this.getSelectedServicesCount();
                });
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 处理服务节点状态变化
        async handleServiceNodeChange(node, data, isChecked) {
            // 如果服务被选中，需要确保工具已加载
            if (isChecked) {
                // 检查是否已有子节点（工具）
                if (!node.childNodes || node.childNodes.length === 0) {
                    // 如果没有子节点，主动加载工具列表
                    // ensureToolsLoaded方法内部已经处理了状态恢复，这里不需要额外处理
                    await this.ensureToolsLoaded(node);

                    // 工具加载完成后，再次检查并选中所有有效工具
                    this.$nextTick(() => {
                        this.selectAllValidToolsForService(data.id, true);
                    });
                } else {
                    // 如果已有子节点，直接选中所有有效的工具
                    this.selectAllValidToolsForService(data.id, true);
                }
            } else {
                // 取消选中所有工具
                this.selectAllValidToolsForService(data.id, false);
            }
        },

        // 处理工具节点状态变化
        handleToolNodeChange(node) {
            // 工具状态变化时，需要更新父服务节点的状态
            const parentNode = node.parent;
            if (parentNode && parentNode.data && !parentNode.data.isTool) {
                this.$nextTick(() => {
                    this.updateParentServiceState(parentNode);
                });
            }
        },

        // 更新父服务节点的状态
        updateParentServiceState(serviceNode) {
            if (!serviceNode || !serviceNode.childNodes) return;

            // 获取所有有效且未禁用的工具节点
            const enabledValidTools = serviceNode.childNodes.filter((child) => {
                return child.data.isTool && child.data.isValid === 1 && !child.data.disabled;
            });

            // 如果没有有效且未禁用的工具，服务节点应该取消选中
            if (enabledValidTools.length === 0) {
                const serviceNodeRef = this.$refs.mcpTree.getNode(serviceNode.data.id);
                if (serviceNodeRef) {
                    serviceNodeRef.setChecked(false, false);
                    serviceNodeRef.indeterminate = false;
                }
                return;
            }

            // 检查有效且未禁用的工具中有多少被选中
            const checkedEnabledTools = enabledValidTools.filter((child) => {
                const toolNodeRef = this.$refs.mcpTree.getNode(child.data.id);
                return toolNodeRef && toolNodeRef.checked;
            });

            const serviceNodeRef = this.$refs.mcpTree.getNode(serviceNode.data.id);
            if (!serviceNodeRef) return;

            if (checkedEnabledTools.length === 0) {
                // 没有有效工具被选中，取消选中服务
                serviceNodeRef.setChecked(false, false);
                serviceNodeRef.indeterminate = false;
            } else if (checkedEnabledTools.length === enabledValidTools.length) {
                // 所有有效工具都被选中，选中服务
                serviceNodeRef.setChecked(true, false);
                serviceNodeRef.indeterminate = false;
            } else {
                // 部分有效工具被选中，设置为半选状态
                serviceNodeRef.setChecked(false, false);
                serviceNodeRef.indeterminate = true;
            }
        },
        // 获取选中的服务数量（包括半选状态）
        getSelectedServicesCount() {
            if (!this.$refs.mcpTree) return 0;

            const checkedNodes = this.$refs.mcpTree.getCheckedNodes(false, true);
            // 只计算父节点（服务）
            const checkedServices = checkedNodes.filter((node) => !node.isTool);
            // 返回选中和半选的服务总数
            this.selectedServicesCount = checkedServices.length;
        },

        // 更新选中状态缓存
        updateCheckedCache() {
            if (!this.$refs.mcpTree) return;

            const checkedKeys = this.$refs.mcpTree.getCheckedKeys();
            this.checkedKeysCache = [...checkedKeys];

            // 更新选中节点缓存
            this.selectedNodeCache.clear();
            checkedKeys.forEach((key) => {
                this.selectedNodeCache.add(key);
            });
        },

        // 恢复选中状态
        restoreCheckedState() {
            if (!this.$refs.mcpTree || this.checkedKeysCache.length === 0) return;

            // 设置标志防止递归调用
            this.isUpdatingTreeState = true;

            try {
                // 分离服务节点和工具节点
                const serviceKeys = [];
                const toolKeys = [];

                this.checkedKeysCache.forEach((key) => {
                    const node = this.$refs.mcpTree.getNode(key);
                    if (node) {
                        if (node.data.isTool) {
                            // 只恢复有效且未禁用的工具节点
                            if (node.data.isValid === 1 && !node.data.disabled) {
                                toolKeys.push(key);
                            }
                        } else {
                            serviceKeys.push(key);
                        }
                    }
                });

                // 先恢复工具节点的选中状态
                if (toolKeys.length > 0) {
                    toolKeys.forEach((toolKey) => {
                        const toolNode = this.$refs.mcpTree.getNode(toolKey);
                        if (toolNode) {
                            toolNode.setChecked(true, false);
                        }
                    });
                }

                // 然后更新所有服务节点的状态
                this.$nextTick(() => {
                    this.updateAllServiceStates();
                });
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 更新所有服务节点的状态
        updateAllServiceStates() {
            if (!this.$refs.mcpTree) return;

            // 获取所有服务节点
            this.mcpServices.forEach((service) => {
                const serviceNode = this.$refs.mcpTree.getNode(service.id);
                if (serviceNode && serviceNode.childNodes) {
                    this.updateParentServiceState(serviceNode);
                }
            });
        },

        // 恢复特定的选中状态（用于工具加载后的状态恢复）
        restoreSpecificCheckedState(checkedKeys) {
            if (!this.$refs.mcpTree || !checkedKeys || checkedKeys.length === 0) return;

            // 设置标志防止递归调用
            this.isUpdatingTreeState = true;

            try {
                // 过滤出当前存在的节点ID
                const existingKeys = checkedKeys.filter((key) => {
                    return this.$refs.mcpTree.getNode(key) !== null;
                });

                if (existingKeys.length > 0) {
                    // 分离服务节点和工具节点
                    const toolKeys = [];

                    existingKeys.forEach((key) => {
                        const node = this.$refs.mcpTree.getNode(key);
                        if (node && node.data.isTool) {
                            // 只恢复有效且未禁用的工具节点
                            if (node.data.isValid === 1 && !node.data.disabled) {
                                toolKeys.push(key);
                            }
                        }
                    });

                    // 先恢复工具节点的选中状态
                    if (toolKeys.length > 0) {
                        toolKeys.forEach((toolKey) => {
                            const toolNode = this.$refs.mcpTree.getNode(toolKey);
                            if (toolNode) {
                                toolNode.setChecked(true, false);
                            }
                        });
                    }

                    // 然后更新所有服务节点的状态
                    this.$nextTick(() => {
                        this.updateAllServiceStates();
                        // 更新缓存
                        this.updateCheckedCache();
                    });
                }
            } finally {
                this.isUpdatingTreeState = false;
            }
        },

        // 确保工具已加载
        async ensureToolsLoaded(node) {
            if (!node || node.data.isTool) return;

            try {
                // 如果节点还没有子节点，触发懒加载
                if (!node.childNodes || node.childNodes.length === 0) {
                    // 保存当前的选中状态
                    let currentCheckedKeys = [];
                    if (this.$refs.mcpTree) {
                        currentCheckedKeys = this.$refs.mcpTree.getCheckedKeys();
                    }

                    await new Promise((resolve) => {
                        this.lazyLoadNode(node, (tools) => {
                            // 直接更新对应服务的children，避免重新赋值整个数组
                            const serviceIndex = this.mcpServices.findIndex(
                                (item) => item.id === node.data.id
                            );
                            if (serviceIndex !== -1) {
                                // 使用Vue.set确保响应式更新，但不触发整个数组的重新渲染
                                this.$set(this.mcpServices[serviceIndex], 'children', tools);
                            }

                            // 在下一个tick恢复选中状态
                            this.$nextTick(() => {
                                if (currentCheckedKeys.length > 0) {
                                    this.restoreSpecificCheckedState(currentCheckedKeys);
                                }
                                resolve();
                            });
                        });
                    });
                }
            } catch (error) {
                console.error('加载工具失败:', error);
            }
        },

        // 选中/取消选中服务下的所有有效工具
        selectAllValidToolsForService(serviceId, isChecked) {
            if (!this.$refs.mcpTree || this.isUpdatingTreeState) return;

            const serviceNode = this.$refs.mcpTree.getNode(serviceId);
            if (!serviceNode || !serviceNode.childNodes) return;

            // 获取所有有效且未禁用的工具节点ID
            const enabledValidToolIds = serviceNode.childNodes
                .filter(
                    (child) => child.data.isTool && child.data.isValid === 1 && !child.data.disabled
                )
                .map((child) => child.data.id);

            if (enabledValidToolIds.length > 0) {
                // 临时设置标志，防止递归调用
                const wasUpdating = this.isUpdatingTreeState;
                this.isUpdatingTreeState = true;

                try {
                    enabledValidToolIds.forEach((toolId) => {
                        const toolNode = this.$refs.mcpTree.getNode(toolId);
                        if (toolNode) {
                            toolNode.setChecked(isChecked, false);
                        }
                    });

                    // 更新缓存
                    this.$nextTick(() => {
                        if (!wasUpdating) {
                            this.updateCheckedCache();
                        }
                    });
                } finally {
                    this.isUpdatingTreeState = wasUpdating;
                }
            }
        }
    },
    watch: {
        // 监听 selectedModel prop 的变化
        selectedModel: {
            handler(newVal) {
                if (newVal && newVal !== this.currentModel) {
                    this.currentModel = newVal;
                }
            },
            immediate: true
        },

        // 监听搜索文本变化，触发树形组件过滤
        mcpSearchText(val) {
            if (this.$refs.mcpTree) {
                this.$refs.mcpTree.filter(val);
            }
        }
    }
};
</script>

<style scoped lang="less">
.message-input {
    .input-container {
        display: flex;
        flex-direction: column;
        width: 60rem;
        height: 8.75rem;
        background: rgba(255, 255, 255, 0.92);
        border-radius: 0.75rem;
        border: 0.0625rem solid #1765ff;
        padding: 0.75rem;
        gap: 0.75rem;
        transition: opacity 0.3s ease;

        .chat-input {
            min-height: 0;
            flex: 1;
            resize: none;
            border: none;
            outline: none;
            font-family: HONORSansCN, HONORSansCN;
            font-weight: 400;
            color: #222222;
            font-size: 1rem;
            line-height: 1.375rem;

            &::placeholder {
                font-family: HONORSansCN, HONORSansCN;
                font-weight: 400;
                font-size: 1rem;
                color: #999999;
            }
        }

        .input-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .model-select {
                width: 9.125rem;
                height: 2rem;

                /deep/ .el-input__inner {
                    background: transparent;
                    border-radius: 0.5rem;
                    border: 0.0625rem solid #e0e0e0;
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: #222222;
                }
            }
            .mcp-select {
                min-width: 9.125rem;
                height: 2rem;
                background: transparent;
                border-radius: 0.5rem;
                border: 0.0625rem solid #e0e0e0;
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                user-select: none;
                .selected-count {
                    color: #1565ff;
                    font-weight: 600;
                    margin-left: 0.25rem;
                }
                &:hover {
                    border-color: #1765ff;
                }
            }

            .right-actions {
                margin-left: auto;
                display: flex;
                align-items: center;
                gap: 1rem;

                .mic-btn {
                    width: 2rem;
                    height: 2rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: transparent;
                    border-radius: 0.375rem;
                    cursor: pointer;

                    .el-icon-microphone {
                        font-size: 1.25rem;
                        color: #0c1827;
                    }

                    &:hover {
                        background: rgba(2, 101, 254, 0.1);
                    }
                }

                .send-btn {
                    width: 2rem;
                    height: 2rem;
                    border-radius: 50%;
                    padding: 0;
                    background: #87b3fe;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: background-color 0.2s;

                    .el-icon-top {
                        color: #fff;
                        font-weight: 600;
                        font-size: 1rem;
                        transform: translateY(0.0625rem);
                    }

                    .el-icon-close {
                        color: #fff;
                        font-weight: 600;
                        font-size: 1rem;
                    }

                    &.is-stop {
                        background: #f56565;

                        &:hover {
                            background: #e53e3e;
                        }
                    }

                    &.is-disabled {
                        background: rgb(200, 202, 217);
                        cursor: default;
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
.model-select-popper {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow:
        0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    .el-select-dropdown__item {
        height: fit-content;
        line-height: normal;
        padding: 0;
        &.hover {
            background: transparent;
        }
        &:hover {
            background: #f2f2f2;
        }
        &.selected {
            background: #f2f2f2;
        }
    }
    .el-select-dropdown__list {
        padding: 0;
    }
    .model-option {
        height: 3.5rem;
        border-radius: 0.5rem;
        display: grid;
        grid-template-columns: 1fr 1.25rem;
        align-items: center;
        gap: 0.5rem;
        padding: 0.625rem;
        .model-option-text {
            display: flex;
            flex-direction: column;
            height: fit-content;
            flex: 1;
            .model-name {
                font-weight: 500;
                font-size: 0.875rem;
                color: #222222;
                line-height: 1.25rem;
            }
            .model-desc {
                font-weight: 400;
                font-size: 0.75rem;
                color: #666666;
                line-height: 1.125rem;
            }
        }
        .model-selected {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1rem;
            color: #222222;
        }
    }
}
.mic-popper {
    &.el-tooltip__popper[x-placement^='top'] .popper__arrow {
        bottom: -5px;
    }
}
.mcp-popover {
    padding: 0.75rem;
    width: fit-content;
    background: #ffffff;
    box-shadow:
        0rem 0.5625rem 1.75rem 0.5rem rgba(0, 0, 0, 0.05),
        0rem 0.375rem 1rem 0rem rgba(0, 0, 0, 0.08),
        0rem 0.1875rem 0.375rem -0.25rem rgba(0, 0, 0, 0.12);
    border-radius: 0.75rem;
    // MCP Popover 样式
    .mcp-popover-content {
        padding: 0;
        height: 12rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .mcp-search-box {
            padding: 0.75rem 0;
            border-bottom: 1px solid #ebeef5;
        }

        .mcp-service-tree {
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 0;

            // 加载状态样式
            .loading-container,
            .error-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2rem 1rem;
                color: #909399;
                font-size: 0.875rem;

                i {
                    font-size: 1.5rem;
                    margin-bottom: 0.5rem;
                }

                .el-button {
                    margin-top: 0.5rem;
                }
            }

            .error-container {
                color: #f56c6c;

                i {
                    color: #f56c6c;
                }
            }

            .loading-more,
            .no-more-data {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0.5rem;
                color: #909399;
                font-size: 0.75rem;

                i {
                    margin-right: 0.25rem;
                }
            }

            .no-more-data {
                color: #c0c4cc;
            }

            // 自定义树形组件样式
            .el-tree {
                background: transparent;

                .el-tree-node {
                    .el-tree-node__content {
                        height: auto;

                        .el-tree-node__expand-icon {
                            color: #606266;
                            font-size: 0.75rem;
                            transition: transform 0.2s ease;

                            // 展开状态的图标旋转
                            &.expanded {
                                transform: rotate(90deg);
                            }

                            // 加载状态
                            &.is-loading {
                                animation: rotating 1s linear infinite;
                            }
                        }

                        // 加载动画
                        @keyframes rotating {
                            from {
                                transform: rotate(0deg);
                            }
                            to {
                                transform: rotate(360deg);
                            }
                        }

                        .el-checkbox {
                            margin-right: 0.5rem;
                            margin-bottom: 0;
                        }
                    }

                    // 父节点样式
                    &.is-expanded > .el-tree-node__content {
                        font-weight: 500;
                        color: #303133;
                    }

                    // 子节点样式
                    .el-tree-node {
                        .el-tree-node__content {
                            padding-left: 1.5rem;
                            font-size: 0.875rem;
                            color: #606266;
                        }
                    }
                }

                .custom-tree-node {
                    min-width: 0;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    .node-label {
                        font-size: 0.875rem;
                        line-height: 1.25rem;
                        width: 100%;
                    }

                    .node-description {
                        font-size: 0.75rem;
                        color: #909399;
                        line-height: 1.2;
                        width: 100%;
                    }
                }
            }
        }
    }
}
</style>
