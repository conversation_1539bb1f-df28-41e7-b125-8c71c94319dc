<template>
    <nav class="nav-bar" :class="styleType === 1 ? 'nav-bar-1' : ''" @click="handleGlobalClick">
        <div class="logo">
            <img src="@/img/homePage/logo.png" alt="logo" class="logo-img" />
            <span class="logo-text">MCP服务清单管理平台</span>
        </div>
        <div class="menu">
            <ul class="menu-list">
                <li
                    v-for="item in menuList"
                    :key="item.value"
                    class="menu-item"
                    :class="{
                        active: isActive(item),
                        disabled: item.disabled,
                        open: openMenus.includes(item.value)
                    }"
                    @click="handleMenuItemClick(item, $event)"
                    :aria-haspopup="!!item.children"
                    :aria-expanded="item.children ? openMenus.includes(item.value) : undefined"
                    role="menuitem"
                >
                    <span class="menu-label">
                        {{ item.label }}
                        <i v-if="item.children" class="el-icon-arrow-down arrow-icon"></i>
                    </span>
                    <ul
                        v-if="item.children"
                        class="submenu"
                        :class="{ visible: openMenus.includes(item.value) }"
                        role="menu"
                    >
                        <li
                            v-for="child in item.children"
                            :key="child.value"
                            class="submenu-item"
                            :class="{
                                active: activeIndex === child.value,
                                disabled: child.disabled
                            }"
                            @click="handleSubmenuClick(child, $event)"
                            role="menuitem"
                        >
                            {{ child.label }}
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</template>
<script>
export default {
    name: 'NavBar',
    props: {
        styleType: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            activeIndex: 'home',
            openMenus: [],
            menuList: [
                { label: '首页', value: 'home' },
                {
                    label: '新增配置项',
                    value: 'config',
                    children: [
                        { label: 'MCP工具配置', value: 'configTool' },
                        { label: 'MCP资源配置', value: 'configResource' },
                        { label: 'MCP服务配置', value: 'configService' }
                    ]
                },
                {
                    label: '权限配置管理',
                    value: 'permission'
                },
                {
                    label: '大模型调用测试',
                    value: 'modelTest'
                }
            ]
        };
    },
    created() {
        const origin = window.location.href;
        const lastItem = origin.substring(origin.lastIndexOf('/') + 1);
        const isSub = lastItem.split('-');
        if (isSub.length === 1) {
            this.activeIndex = this.$route.name;
        } else {
            this.activeIndex = 'home';
            this.$emit('jumpRouter', 'home');
        }
    },
    mounted() {
        document.addEventListener('click', this.handleOutsideClick);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleOutsideClick);
    },
    methods: {
        // 判断菜单项是否激活
        isActive(item) {
            if (item.children && item.children.length) {
                return item.children.some((child) => child.value === this.activeIndex);
            }
            return this.activeIndex === item.value;
        },
        // 处理菜单项点击
        handleMenuItemClick(item, event) {
            if (item.disabled) return;

            if (!item.children) {
                // 没有子菜单，直接跳转
                this.handleClick(item);
                return;
            }
        },
        // 处理子菜单项点击
        handleSubmenuClick(child, event) {
            if (child.disabled) return;
            event.stopPropagation();
            this.handleClick(child);
        },
        // 切换子菜单显示状态
        toggleSubmenu(item) {
            const index = this.openMenus.indexOf(item.value);
            if (index > -1) {
                this.openMenus.splice(index, 1);
            } else {
                this.openMenus = [item.value]; // 只允许一个菜单打开
            }
        },
        // 处理路由跳转
        handleClick(item) {
            if (this.activeIndex === item.value) return;
            console.log(`点击了${item.label}`);
            this.activeIndex = item.value;
            this.openMenus = []; // 关闭所有子菜单
            this.$emit('jumpRouter', item.value);
        },
        handleGlobalClick(event) {
            event.stopPropagation();
        },
        handleOutsideClick(event) {
            if (!this.$el.contains(event.target)) {
                this.openMenus = [];
            }
        }
    }
};
</script>
<style scoped lang="less">
.nav-bar {
    height: 4rem;
    display: flex;
    align-items: center;
    background: #1565ff;
    box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.2);
    position: relative;
    .logo {
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        &-img {
            user-select: none;
            width: 2.25rem;
            height: 2.25rem;
        }
        &-text {
            margin-left: 0.75rem;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 500;
            font-size: 1.375rem;
            color: #ffffff;
            line-height: 2rem;
        }
    }
    .menu {
        flex: 1;
        display: flex;
        justify-content: center;
        height: 100%;
        user-select: none;
        .menu-list {
            display: flex;
            align-items: center;
            gap: 1.25rem;
            list-style: none;
            margin: 0;
            padding: 0;
            height: 100%;
            .menu-item {
                position: relative;
                display: flex;
                align-items: center;
                padding: 0 1rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                color: #ffffffd9;
                cursor: pointer;
                height: 2.25rem;
                transition: all 0.2s ease;

                .arrow-icon {
                    font-size: 1rem;
                    transition: transform 0.3s ease;
                }

                // 桌面端hover效果
                &:hover {
                    background-color: rgba(255, 255, 255, 0.15);
                    border-radius: 0.375rem;
                }

                // 移动端或点击打开状态
                &.open {
                    background-color: rgba(255, 255, 255, 0.15);
                    border-radius: 0.375rem;
                }

                &.active {
                    color: #ffffff;
                    background-color: rgba(255, 255, 255, 0.15);
                    border-radius: 0.375rem;
                }

                &.disabled {
                    color: #ffffff88;
                    cursor: not-allowed;
                    background: transparent;

                    &:hover {
                        background-color: transparent;
                    }
                }
                .submenu {
                    position: absolute;
                    left: 0;
                    top: 100%;
                    color: #333;
                    border-radius: 0.375rem;
                    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.15);
                    background: #ffffff;
                    list-style: none;
                    padding: 0.5rem 0;
                    margin-top: 0.5rem;
                    z-index: 1000;
                    min-width: 100%;

                    // 默认隐藏状态
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-0.5rem);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                    // 显示状态
                    &.visible {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }

                    .submenu-item {
                        padding: 0.75rem 1rem;
                        white-space: nowrap;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        font-size: 1rem;

                        &.active {
                            background: #c4e2ff;
                            color: #1565ff;
                            font-weight: 600;

                            &:hover {
                                background: #c4e2ff;
                                color: #1565ff;
                            }
                        }

                        &:hover {
                            background: #f0f8ff;
                            color: #1565ff;
                        }

                        &.disabled {
                            color: #999;
                            cursor: not-allowed;
                            background: transparent;

                            &:hover {
                                background: transparent;
                                color: #999;
                            }
                        }
                    }
                }

                &:hover {
                    .submenu {
                        opacity: 1;
                        visibility: visible;
                        transform: translateY(0);
                    }
                    .arrow-icon {
                        transform: rotate(180deg);
                    }
                }

                &.open .submenu {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                    .arrow-icon {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }
}
.nav-bar-1 {
    background: #fff !important;

    .logo {
        display: none !important;
    }

    .menu-item {
        color: #409eff !important;

        &:hover,
        &.open {
            color: #ffffff !important;
            background-color: #409eff !important;
        }

        &.active {
            color: #ffffff !important;
            background-color: #409eff !important;
        }
    }
}
</style>
