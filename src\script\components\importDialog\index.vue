<template>
    <el-dialog
        :title="title || typesToTitle[type]"
        :visible.sync="dialogVisible"
        :width="width"
        :close-on-click-modal="false"
        :before-close="handleClose"
        destroy-on-close
    >
        <ByExcel v-if="type === 'excel'" @on-success="onSuccess" @on-cancel="onCancel" />
        <ByJson v-else-if="type === 'json'" @on-success="onSuccess" @on-cancel="onCancel" />
        <ByCurl v-else-if="type === 'curl'" @on-success="onSuccess" @on-cancel="onCancel" />
        <template v-else>
            <div class="error-msg">未知导入类型</div>
        </template>
    </el-dialog>
</template>

<script>
const typesToTitle = {
    excel: '从 Excel 导入',
    json: '从 JSON 导入',
    curl: '从 Curl 导入'
};

export default {
    name: 'ImportDialog',
    components: {
        ByExcel: () => import('./ByExcel.vue'),
        ByJson: () => import('./ByJson.vue'),
        ByCurl: () => import('./ByCurl.vue')
    },
    props: {
        // 导入类型
        type: {
            type: String,
            required: true,
            validator: (value) => Object.keys(typesToTitle).includes(value)
        },
        // 弹窗标题
        title: {
            type: String,
            default: ''
        },
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: '760px'
        }
    },
    data() {
        return {
            dialogVisible: this.visible,
            typesToTitle
        };
    },
    watch: {
        visible(val) {
            this.dialogVisible = val;
        },
        dialogVisible(val) {
            this.$emit('update:visible', val);
        }
    },
    methods: {
        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
            this.$emit('on-cancel');
        },
        // 导入成功
        onSuccess(data) {
            this.$emit('on-success', data);
            // 延迟关闭弹窗，确保父组件有时间处理数据
            this.$nextTick(() => {
                this.dialogVisible = false;
            });
        },
        // 取消导入
        onCancel() {
            this.$emit('on-cancel');
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="less" scoped>
.error-msg {
    color: #f56c6c;
    text-align: center;
    padding: 20px;
}
</style>
