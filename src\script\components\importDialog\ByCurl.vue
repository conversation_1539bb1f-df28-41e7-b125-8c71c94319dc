<template>
    <div class="curl-import">
        <!-- <div class="warning-tip">
            <i class="el-icon-warning"></i>
            当前仅支持导入curl(bash)格式
        </div> -->
        <div class="main-content">
            <div class="input-curl">
                <span>输入curl</span>
                <textarea
                    v-model.trim="curlContent"
                    :placeholder="curlPlaceholder"
                    class="input-content custom-scrollbar"
                    type="textarea"
                    :rows="16"
                    :resize="false"
                ></textarea>
            </div>
            <div class="output-json">
                <span>输出json</span>
                <textarea
                    v-model.trim="jsonContent"
                    :placeholder="jsonPlaceholder"
                    class="input-content custom-scrollbar"
                    type="textarea"
                    :rows="16"
                    :resize="false"
                ></textarea>
            </div>
        </div>

        <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
    </div>
</template>

<script>
import { convertCurlToToolData } from '@/script/utils/method';

export default {
    name: 'ByCurl',
    data() {
        return {
            curlContent: '',
            curlError: false,
            curlPlaceholder:
                '// 示例 curl\n' +
                `curl --request POST \\
  --url http://***********:8080/example \\
  --header 'Content-Type: application/json' \\
  --data '{
    "id": ["123456"],
    "config": {
      "enabled": true,
      "timeout": 30
    }
}'

// 示例 curl (表单格式)
// curl --request POST \\
//   --url http://***********:8080/form \\
//   --header 'Content-Type: application/x-www-form-urlencoded' \\
//   --data 'username=admin&password=123456&remember=true'`,
            jsonContent: '',
            jsonError: false,
            jsonPlaceholder:
                '// 示例 JSON\n' +
                JSON.stringify(
                    {
                        code: 0,
                        message: '成功',
                        data: [
                            {
                                id: '123456',
                                name: '示例',
                                time: '1971-01-01 00:00:00'
                            }
                        ]
                    },
                    null,
                    2
                )
        };
    },
    methods: {
        // 自动格式化JSON并验证JSON格式
        formatJson() {
            try {
                if (this.jsonContent.trim() === '') {
                    this.jsonError = false;
                    return;
                }
                const parsed = JSON.parse(this.jsonContent);

                if (typeof parsed !== 'object' || parsed === null) {
                    throw new Error('必须是对象或数组');
                }

                this.jsonContent = JSON.stringify(JSON.parse(this.jsonContent), null, 2);
                this.jsonError = false;
            } catch (e) {
                this.jsonError = true;
                this.$message.error(`JSON格式错误: ${e.message}`);
            }
        },
        // 确认导入
        handleConfirm() {
            // 检查必要的输入
            if (this.curlContent.trim() === '') {
                this.$message.warning('请输入 curl 命令');
                return;
            }

            // if (this.jsonContent.trim() === '') {
            //     this.$message.warning('请输入 JSON 响应数据');
            //     return;
            // }

            // 验证 JSON 格式
            this.formatJson();
            if (this.jsonError) return;

            try {
                // 解析 curl 命令和 JSON 数据，转换为工具数据格式
                const toolData = convertCurlToToolData(this.curlContent, this.jsonContent);

                // 发送解析后的结果
                const result = {
                    success: true,
                    // data: this.jsonContent, // 保持原有的 JSON 数据
                    toolData: toolData // 新增解析后的工具数据
                };

                this.$emit('on-success', result);
            } catch (error) {
                console.error('解析 curl 命令失败:', error);
                this.$message.error(`解析 curl 命令失败: ${error.message}`);
            }
        },
        // 取消导入
        handleCancel() {
            this.$emit('on-cancel');
        }
    }
};
</script>

<style lang="less" scoped>
.curl-import {
    padding: 0 20px;

    .warning-tip {
        background: #fff6e6;
        border: 1px solid #ffe58f;
        padding: 10px 12px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);

        .el-icon-warning {
            color: #ff7802;
            margin-right: 8px;
        }
    }
    .main-content {
        height: 50vh;
        display: flex;
        gap: 20px;
        .input-curl,
        .output-json {
            min-width: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
    }
    .input-content {
        width: 100%;
        min-height: 0;
        flex: 1;
        outline: none;
        resize: none;
        background: #f6f8fa;
        border-radius: 4px;
        border: 1px dashed #d6dae0;
        font-size: 14px;
        padding: 16px;
        &::placeholder {
            color: rgba(0, 0, 0, 0.35);
        }
    }

    .dialog-footer {
        text-align: right;
        margin-top: 20px;
    }
}
</style>
