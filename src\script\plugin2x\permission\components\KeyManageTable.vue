<template>
    <div class="key-manage-table">
        <!-- 表头 -->
        <div class="header-row grid-row" :style="{ gridTemplateColumns: computeGridRow }">
            <div class="cell">授权开始时间</div>
            <div class="cell">授权结束时间</div>
            <div class="cell">Token</div>
            <div class="cell">创建于</div>
            <div class="cell" v-if="!readonly">操作</div>
        </div>

        <!-- 内容区域 -->
        <div class="content-wrapper" v-if="!readonly || internalData.length > 0">
            <!-- 数据行容器 -->
            <div class="data-rows-wrapper custom-scrollbar">
                <div
                    v-for="(row, index) in internalData"
                    :key="index"
                    class="data-row grid-row"
                    :style="{ gridTemplateColumns: computeGridRow }"
                >
                    <!-- 授权开始时间 -->
                    <div class="cell">
                        <el-date-picker
                            v-if="!readonly"
                            v-model="row.authStartTime"
                            type="date"
                            value-format="yyyy-MM-dd"
                            format="yyyy-MM-dd"
                            placeholder="请选择"
                            :editable="false"
                            :clearable="false"
                            :picker-options="{
                                disabledDate: (time) => disabledStartDate(time, row.authEndTime)
                            }"
                            class="date-picker cell-input"
                            @change="handleStartDateChange($event, row)"
                        />
                        <el-input
                            v-else
                            v-model="row.authStartTime"
                            placeholder=""
                            readonly
                            class="cell-input readonly"
                        />
                    </div>

                    <!-- 授权结束时间 -->
                    <div class="cell">
                        <el-date-picker
                            v-if="!readonly"
                            v-model="row.authEndTime"
                            type="date"
                            value-format="yyyy-MM-dd"
                            format="yyyy-MM-dd"
                            placeholder="请选择"
                            :editable="false"
                            :clearable="false"
                            :picker-options="{
                                disabledDate: (time) => disabledEndDate(time, row.authStartTime)
                            }"
                            class="date-picker cell-input"
                            @change="handleEndDateChange($event, row)"
                        />
                        <el-input
                            v-else
                            v-model="row.authEndTime"
                            placeholder=""
                            readonly
                            class="cell-input readonly"
                            :style="computeInputColor(row)"
                        />
                    </div>

                    <!-- Token -->
                    <div class="cell" @click.stop="showCopy && copyToken(row)">
                        <el-input
                            v-model="row.mcpSecretKey"
                            placeholder=""
                            readonly
                            class="token-input cell-input readonly"
                            :style="computeInputColor(row)"
                        />
                        <div v-if="showCopy" class="copy-token" @click.stop="copyToken(row)">
                            复制
                        </div>
                    </div>

                    <!-- 创建于 -->
                    <div class="cell">
                        <el-input
                            v-model="row.createdTime"
                            placeholder=""
                            readonly
                            class="created-at-input cell-input readonly"
                        />
                    </div>

                    <!-- 操作 -->
                    <div class="cell op-cell" v-if="!readonly">
                        <span class="op op-delete" @click="handleDelete(row, index)">删除</span>
                        <span
                            class="op op-refresh"
                            @click="handleRefresh(row, index)"
                            v-if="row.mcpSecretKey && row.createdTime"
                            >刷新</span
                        >
                    </div>
                </div>

                <!-- 新增密钥 -->
                <div class="add-key" @click="addRow" v-if="!readonly"><i>＋</i> 新增密钥</div>
            </div>
        </div>
        <el-empty
            v-else
            description="暂无数据"
            :image="require('@/img/common/noDataMon.png')"
            style="width: 100%; height: 100%"
        >
        </el-empty>
    </div>
</template>

<script>
import { copyText, confirmWarning } from '@/script/utils/method';

export default {
    name: 'KeyManageTable',
    props: {
        value: {
            type: Object,
            default: () => ({
                originList: [],
                newList: []
            })
        },
        readonly: {
            type: Boolean,
            default: false
        },
        showCopy: {
            type: Boolean,
            default: false
        },
        gridRow: {
            type: String,
            default: ''
        }
    },
    computed: {
        internalData: {
            get() {
                const originList = this.value.originList || [];
                const newList = this.value.newList || [];
                return [...originList, ...newList];
            },
            set(val) {
                const originLength = (this.value.originList || []).length;
                const newValue = {
                    originList: val.slice(0, originLength),
                    newList: val.slice(originLength)
                };
                this.$emit('input', newValue);
            }
        },
        computeGridRow: {
            get() {
                if (this.gridRow) {
                    return this.gridRow;
                }
                if (this.readonly) {
                    return '1fr 1fr 1fr 1fr';
                }
                return '180px 180px 1fr 200px 150px';
            }
        }
    },
    methods: {
        copyToken(row) {
            copyText(row.mcpSecretKey)
                .then((res) => {
                    this.$message.success(res);
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        computeInputColor(row) {
            if (row.isExpired === true) {
                return '--input-color: #F74041';
            }
            if (row.isExpired === false) {
                return '--input-color: #0DC05C';
            }
            return '--input-color: rgba(0, 0, 0, 0.85)';
        },
        // 开始日期的禁用规则
        disabledStartDate(time, endDate) {
            if (!endDate) return false;
            return time.getTime() > new Date(endDate).getTime();
        },
        // 结束日期的禁用规则
        disabledEndDate(time, startDate) {
            if (!startDate) return false;
            return time.getTime() < new Date(startDate).getTime();
        },
        // 开始日期变化时的处理
        handleStartDateChange(date, row) {
            if (row.authEndTime && new Date(date) > new Date(row.authEndTime)) {
                row.authEndTime = '';
            }
            const newData = [...this.internalData];
            this.internalData = newData;
        },
        // 结束日期变化时的处理
        handleEndDateChange(date, row) {
            if (row.authStartTime && new Date(date) < new Date(row.authStartTime)) {
                row.authStartTime = '';
            }
            const newData = [...this.internalData];
            this.internalData = newData;
        },
        addRow() {
            const newData = [...this.internalData];
            newData.push({
                authStartTime: '',
                authEndTime: '',
                mcpSecretKey: '',
                createdTime: ''
            });
            this.internalData = newData;
        },
        handleDelete(row, index) {
            // 若 Token 为空 认为是新增的 直接删除
            if (!row.mcpSecretKey) {
                const newData = [...this.internalData];
                newData.splice(index, 1);
                this.internalData = newData;
                return;
            }
            confirmWarning(
                this,
                {
                    title: '确定删除该密钥吗？',
                    desc: '删除后将无法恢复'
                },
                () => {
                    this.$emit('on-delete', row);
                }
            );
        },
        handleRefresh(row) {
            this.$emit('on-refresh', row);
        }
    }
};
</script>

<style lang="less" scoped>
.key-manage-table {
    display: flex;
    flex-direction: column;
    border: 1px solid #ebedf0;
    border-radius: 2px;
    background: #ffffff;
    height: 100%;
    overflow-y: auto;

    .grid-row {
        display: grid;
    }

    .header-row {
        height: 40px;
        background: #f6f8fa;
        .cell {
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-right: 1px solid #ebedf0;
            border-bottom: 1px solid #ebedf0;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);

            &:last-child {
                border-right: none;
            }
        }
    }

    .content-wrapper {
        flex: 1;
        min-height: 0;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .data-rows-wrapper {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        min-height: 40px;
        --bar-width: 4px;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .data-row {
        height: 40px;
        background: #ffffff;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);

        .cell {
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0;
            border-right: 1px solid #ebedf0;
            border-bottom: 1px solid #ebedf0;
            position: relative;
            .copy-token {
                position: absolute;
                right: 0.5rem;
                top: 0;
                font-weight: 400;
                font-size: 14px;
                color: #1565ff;
                cursor: pointer;
                user-select: none;
                transition: color 0.2s ease;
                height: 100%;
                padding: 0 8px;
                display: flex;
                align-items: center;
                background: #ffffff;
                border-radius: 4px;

                &:hover {
                    color: #1565ff99;
                }
            }

            &:last-child {
                border-right: none;
            }

            /deep/ .cell-input {
                --input-color: rgba(0, 0, 0, 0.85);
                height: 100%;
                .el-input__inner {
                    height: 100%;
                    border: 1px solid transparent;
                    border-radius: 0;
                    padding: 0 16px;
                    color: var(--input-color);
                    &::placeholder {
                        color: #00000040;
                    }
                }
            }
            /deep/ .date-picker {
                width: 100%;
                position: relative;
                .el-input__prefix {
                    position: absolute;
                    right: 10px;
                    left: auto;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #00000040;
                }
            }
        }

        .op-cell {
            padding: 0 16px;
            display: flex;
            align-items: center;
            gap: 24px;

            .op {
                user-select: none;
                cursor: pointer;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-size: 14px;
                position: relative;
                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    right: -12px;
                    width: 1px;
                    height: 12px;
                    background: #d6dae0;
                    transform: translateY(-50%);
                }
            }
            .op-delete {
                color: #f74041;
            }
            .op-refresh {
                color: #1565ff;
            }
        }
    }

    .add-key {
        flex: none;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        color: #1565ff;
        cursor: pointer;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        user-select: none;
        background: #ffffff;
        border-top: 1px solid #ebedf0;
        border-bottom: 1px solid #ebedf0;
        position: sticky;
        bottom: 0;
        z-index: 1;

        i {
            font-style: normal;
            margin-right: 4px;
        }
        &:hover {
            color: #1565ffd9;
        }
    }
}
/deep/ .el-input__inner {
    &:not(.readonly > .el-input__inner) {
        &:focus {
            border: 1px solid #1565ff !important;
            box-shadow: 0 0 0 2px rgba(21, 101, 255, 0.2);
        }
    }
}
</style>
