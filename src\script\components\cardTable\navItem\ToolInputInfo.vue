<template>
    <div class="input-info-content">
        <div class="content-left">
            <header class="left-header">
                <img src="@/img/common/note-open.png" alt="" />
                <span>入参结构</span>
            </header>
            <main class="left-main custom-scrollbar">
                <!-- 树形结构 -->
                <el-tree
                    ref="jsonPathTreeIN"
                    class="left-el-tree"
                    :data="treeData"
                    node-key="id"
                    :props="defaultTreeProps"
                    :expand-on-click-node="false"
                    :highlight-current="true"
                    :default-expand-all="true"
                    @node-click="handleNodeClick"
                >
                </el-tree>
            </main>
        </div>
        <div class="content-right">
            <DocTable class="table-content" v-model="tableData" :config="tableConfig" />
        </div>
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';
import { buildTreeFromParams } from '@/script/utils/method.js';

export default {
    name: 'ToolInputInfo',
    components: {
        DocTable
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableConfig: {
                mode: 'normal',
                labelWidth: 150,
                columns: [
                    { label: '参数名称：', prop: 'paramNameEn' },
                    { label: '参数类别：', prop: 'paramInout' },
                    { label: '参数标识：', prop: 'paramName' },
                    { label: '参数类型：', prop: 'paramType' },
                    { label: '参数默认值：', prop: 'paramDefaultValue', singleLine: true },
                    { label: '显示条件：', prop: 'displayConditions', singleLine: true },
                    { label: '参数描述：', prop: 'paramDescription', singleLine: true },
                    { label: '参数路径：', prop: 'paramJsonPath', singleLine: true },
                    { label: '传参方式：', prop: 'isRequired', singleLine: true },
                    { label: '是否起效：', prop: 'isValid', singleLine: true },
                    {
                        label: '其他备注：',
                        prop: 'comment',
                        singleLine: true,
                        textarea: true,
                        fit: true
                    }
                ],
                align: 'top',
                pairCount: 2,
                outBorder: false
            },
            tableData: [
                {
                    paramName: '',
                    paramInout: '',
                    paramType: '',
                    paramNameEn: '',
                    paramDefaultValue: '',
                    displayConditions: '',
                    paramDescription: '',
                    paramJsonPath: '',
                    isRequired: '',
                    isValid: '',
                    comment: ''
                }
            ],
            treeData: [],
            paramList: [],
            defaultTreeProps: {
                label: 'label',
                children: 'children'
            }
        };
    },
    computed: {},
    created() {
        this.initTreeAndTable();
    },
    watch: {
        'value.params': {
            deep: true,
            handler() {
                this.initTreeAndTable();
            }
        }
    },
    methods: {
        initTreeAndTable() {
            if (!Array.isArray(this.value.params) || this.value.params.length === 0) {
                this.treeData = [];
                this.tableData = this.$options.data().tableData;
                return;
            }

            // 仅取 IN 参数
            const inParams = this.value.params.filter((p) => p.paramInout === 'IN');
            this.paramList = inParams;
            this.treeData = buildTreeFromParams(inParams);

            if (inParams.length > 0) {
                this.$nextTick(() => {
                    this.$refs.jsonPathTreeIN.setCurrentKey(inParams[0].paramJsonPath);
                });
                this.setTableData([inParams[0]]);
            } else {
                this.tableData = this.$options.data().tableData;
            }
        },
        setTableData(paramArr) {
            if (!Array.isArray(paramArr)) return;
            this.tableData = paramArr.map((item) => {
                let requiredText = '';
                const requiredMap = [
                    { label: '选填参数', value: 0 },
                    { label: '必填参数', value: 1 },
                    { label: '默认值回填', value: 2 }
                ];
                const requiredItem = requiredMap.find((r) => r.value === item.isRequired);
                requiredText = (requiredItem && requiredItem.label) || '';
                let validText = '';
                item.isValid === 1 && (validText = '是');
                item.isValid === 0 && (validText = '否');
                return {
                    paramName: item.paramName,
                    paramInout: item.paramInout,
                    paramType: item.paramType,
                    paramNameEn: item.paramNameEn,
                    paramDefaultValue: item.paramDefaultValue,
                    displayConditions: item.displayConditions,
                    paramDescription: item.paramDescription,
                    paramJsonPath: item.paramJsonPath,
                    isRequired: requiredText,
                    isValid: validText,
                    comment: item.comment
                };
            });
        },
        handleNodeClick(node) {
            if (node.relatedParams && node.relatedParams.length) {
                this.setTableData(node.relatedParams);
                return;
            }
            const currentPath = `$.${node.id}`;
            let matched = (this.paramList || []).filter((p) => {
                return (
                    typeof p.paramJsonPath === 'string' && p.paramJsonPath.startsWith(currentPath)
                );
            });
            if (matched.length === 0) {
                matched = this.$options.data().tableData;
            }
            this.setTableData(matched);
        }
    }
};
</script>

<style lang="less" scoped>
.input-info-content {
    display: flex;
    gap: 1rem;
    .content-left {
        height: 100%;
        width: 17.625rem;
        display: flex;
        flex-direction: column;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
        .left {
            &-header {
                height: 2.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem 0.25rem 0 0;
                border-bottom: 0.0625rem solid #ebedf0;
                display: flex;
                align-items: center;
                gap: 0.25rem;
                padding: 0 1rem;
                img {
                    width: 0.875rem;
                    height: 0.875rem;
                }
                span {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 500;
                    font-size: 0.875rem;
                    color: rgba(0, 0, 0, 0.85);
                }
            }
            &-main {
                min-height: 0;
                flex: 1;
                padding: 0.5rem;
                overflow-y: auto;
            }
        }
    }
    .content-right {
        height: 100%;
        min-width: 0;
        flex: 1;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
        overflow: hidden;
        .table-content {
            height: 100%;
        }
    }
}

.left-el-tree {
    height: 100%;
    width: 100%;
}
/deep/.el-tree-node__expand-icon {
    &.is-leaf {
        &:before {
            content: '∘';
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
