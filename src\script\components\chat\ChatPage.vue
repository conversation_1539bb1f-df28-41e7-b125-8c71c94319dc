<template>
    <div class="chat-page">
        <div class="chat-title-section">
            <div
                v-if="!isEditingTitle"
                class="chat-title"
                :class="{ 'temporary-session': sessionId && sessionId.startsWith('temp_') }"
                @click="startEditTitle"
                :title="
                    sessionId && sessionId.startsWith('temp_')
                        ? '发送消息后可编辑标题'
                        : '点击编辑标题'
                "
            >
                {{ displayTitle }}
                <i
                    v-if="sessionId && !sessionId.startsWith('temp_')"
                    class="el-icon-edit-outline edit-icon"
                ></i>
            </div>
            <div v-else class="chat-title-editor">
                <el-input
                    ref="titleInput"
                    v-model="editingTitleValue"
                    size="small"
                    :maxlength="50"
                    @blur="saveTitle"
                    @keyup.enter.native="saveTitle"
                    @keyup.esc.native="cancelEditTitle"
                    placeholder="输入对话标题"
                />
            </div>
        </div>
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages custom-scrollbar" ref="messagesContainer">
                <div
                    v-for="(message, index) in filteredMessages"
                    :key="index"
                    class="message-item"
                    :class="{
                        'user-message': message.type === 'user',
                        'assistant-message': message.type === 'assistant',
                        'error-message': message.isError
                    }"
                >
                    <div class="message-content">
                        <!-- 用户消息：纯文本显示 -->
                        <div v-if="message.type === 'user'" class="message-text">
                            {{ message.text }}
                        </div>
                        <!-- AI助手消息：Markdown -->
                        <div v-if="message.type === 'assistant'" class="message-text-container">
                            <MarkdownRenderer
                                :content="getDisplayText(message)"
                                :message-index="index"
                                class="message-text"
                                :class="{ 'streaming-text': isStreamingMessage(index) }"
                            />
                        </div>
                    </div>
                </div>

                <!-- 加载状态 - 只在没有流式消息时显示 -->
                <div
                    v-if="isAiResponding && !hasStreamingMessage"
                    class="message-item assistant-message"
                >
                    <div class="message-content loading-message">
                        <div class="typing-dots">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                </div>

                <!-- 空状态提示 -->
                <div
                    v-if="filteredMessages.length === 0 && !isAiResponding && !isStreamResponse"
                    class="empty-state"
                >
                    <p class="empty-text">开始您的对话吧</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-section">
                <MessageInput
                    :placeholder="placeholder"
                    :model-options="modelOptions"
                    :defaultModel="defaultModel"
                    :selectedModel="selectedModel"
                    :show-mic-button="true"
                    :show-model-details="true"
                    :is-ai-responding="isAiResponding"
                    :is-stream-response="isStreamResponse"
                    popper-class="mcpservice-theme model-select-popper"
                    @send-message="handleSendMessage"
                    @mic-click="handleMicClick"
                    @model-change="handleModelChange"
                    @stop-generation="handleStopGeneration"
                />
                <!-- 回到底部 -->
                <div
                    class="back-bottom"
                    :class="{ 'is-show': showBackToBottom }"
                    @click="scrollToBottom"
                >
                    <i class="el-icon-arrow-down"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import MessageInput from './MessageInput.vue';
import MarkdownRenderer from './MarkdownRenderer.vue';

export default {
    name: 'ChatPage',
    components: {
        MessageInput,
        MarkdownRenderer
    },
    props: {
        // 聊天标题
        title: {
            type: String,
            default: '新对话'
        },
        // 当前会话ID
        sessionId: {
            type: String,
            default: null
        },
        // 聊天消息列表
        messages: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入您的问题...'
        },
        // AI是否正在回复
        isAiResponding: {
            type: Boolean,
            default: false
        },
        // 是否为流式响应
        isStreamResponse: {
            type: Boolean,
            default: false
        },
        // 聊天存储服务
        chatStorageService: {
            type: Object,
            default: null
        },
        // 存储服务是否可用
        isStorageAvailable: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 控制回到底部按钮显示状态
            showBackToBottom: false,
            // 滚动监听器的节流定时器
            scrollThrottleTimer: null,
            // 标题编辑相关状态
            isEditingTitle: false,
            editingTitleValue: '',
            // 流式响应相关状态
            streamingMessageIndex: -1, // 当前正在流式显示的消息索引
            lastScrollTime: 0, // 上次滚动时间，用于节流
            lastGentleScrollTime: 0 // 上次温和滚动时间，用于AI响应期间的节流
        };
    },
    computed: {
        // 显示的标题
        displayTitle() {
            return this.title || '新对话';
        },

        // 过滤后的消息列表（排除空消息）
        filteredMessages() {
            return this.messages.filter((message) => this.isValidMessage(message));
        },

        // 检测是否有流式消息
        hasStreamingMessage() {
            return this.messages.some(
                (message) =>
                    message.type === 'assistant' &&
                    message.isStreaming === true &&
                    this.isValidMessage(message)
            );
        }
    },
    watch: {
        // 监听消息变化，处理流式响应和滚动
        messages: {
            handler(newMessages) {
                // 更新流式消息索引 - 基于过滤后的消息列表
                if (newMessages.length > 0) {
                    // 查找最后一个正在流式输出的有效消息
                    let streamingIndex = -1;
                    const filteredMessages = newMessages.filter((msg) => this.isValidMessage(msg));

                    for (let i = filteredMessages.length - 1; i >= 0; i--) {
                        const message = filteredMessages[i];
                        if (message.type === 'assistant' && message.isStreaming === true) {
                            // 找到原始消息列表中的索引
                            streamingIndex = newMessages.findIndex((msg) => msg === message);
                            break;
                        }
                    }
                    this.streamingMessageIndex = streamingIndex;
                }

                // 检查是否在底部附近，但避免在AI刚开始响应时重复滚动
                const container = this.$refs.messagesContainer;
                if (container && !this.isAiResponding) {
                    const isNearBottom =
                        container.scrollHeight - container.scrollTop - container.clientHeight < 100;
                    if (isNearBottom) {
                        this.throttledScrollToBottom();
                    }
                } else if (container && this.isAiResponding) {
                    // AI响应期间，只在流式更新时滚动
                    const isNearBottom =
                        container.scrollHeight - container.scrollTop - container.clientHeight < 100;
                    if (isNearBottom) {
                        // 使用更温和的滚动，避免频繁滚动
                        this.gentleScrollToBottom();
                    }
                }
            },
            deep: true,
            immediate: false
        },
        // 监听 AI 响应状态变化
        isAiResponding: {
            handler(newVal) {
                if (newVal) {
                    // AI开始响应时，确保加载动画完全渲染后再滚动
                    this.$nextTick(() => {
                        // 使用setTimeout确保DOM完全更新
                        setTimeout(() => {
                            this.scrollToBottom();
                        }, 50); // 50ms延迟确保加载动画渲染完成
                    });
                }
            }
        }
    },
    methods: {
        // 处理发送消息
        handleSendMessage(message) {
            // 添加用户类型标识
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.$emit('send-message', userMessage);
        },

        // 处理停止生成
        handleStopGeneration() {
            this.$emit('stop-generation');
        },

        // 处理麦克风点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    // 使用平滑滚动动画
                    container.scrollTo({
                        top: container.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            });
        },

        // 温和滚动到底部（用于AI响应期间的流式更新）
        gentleScrollToBottom() {
            const now = Date.now();
            if (now - this.lastGentleScrollTime > 200) {
                // 200ms节流，比普通滚动更温和
                this.lastGentleScrollTime = now;
                this.$nextTick(() => {
                    const container = this.$refs.messagesContainer;
                    if (container) {
                        // 使用即时滚动，避免动画冲突
                        container.scrollTo({
                            top: container.scrollHeight,
                            behavior: 'auto'
                        });
                    }
                });
            }
        },

        // 处理滚动事件，控制回到底部按钮显示
        handleScroll() {
            // 使用节流优化性能
            if (this.scrollThrottleTimer) {
                clearTimeout(this.scrollThrottleTimer);
            }

            this.scrollThrottleTimer = setTimeout(() => {
                const container = this.$refs.messagesContainer;
                if (!container) return;

                const scrollTop = container.scrollTop;
                const scrollHeight = container.scrollHeight;
                const clientHeight = container.clientHeight;

                // 计算距离底部的距离
                const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

                // 显示条件：向下滚动超过400px且距离底部超过150px
                const shouldShow = distanceFromBottom > 150;

                this.showBackToBottom = shouldShow;
            }, 100); // 100ms节流
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.$emit('model-change', newModel);
        },

        // 检查是否为正在流式显示的消息
        isStreamingMessage(index) {
            return this.streamingMessageIndex === index;
        },

        // 获取要显示的文本内容（直接返回消息文本，无缓冲）
        getDisplayText(message) {
            return message.text || '';
        },

        // 验证消息是否有效（非空内容）
        isValidMessage(message) {
            if (!message || !message.text) {
                return false;
            }

            // 检查消息内容是否只包含空白字符
            const trimmedText = message.text.trim();
            return trimmedText.length > 0;
        },

        // 节流滚动到底部
        throttledScrollToBottom() {
            const now = Date.now();
            if (now - this.lastScrollTime > 100) {
                // 每100ms最多滚动一次
                this.lastScrollTime = now;
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            }
        },

        // 完成流式响应
        completeStreamResponse() {
            // 重置流式状态
            this.streamingMessageIndex = -1;
            // 最后滚动一次到底部
            this.throttledScrollToBottom();
        },

        // 开始编辑标题
        startEditTitle() {
            // 只有在有会话ID且不是临时会话时才允许编辑
            if (!this.sessionId || this.sessionId.startsWith('temp_')) return;

            this.isEditingTitle = true;
            this.editingTitleValue = this.displayTitle;

            this.$nextTick(() => {
                if (this.$refs.titleInput) {
                    this.$refs.titleInput.focus();
                    this.$refs.titleInput.select();
                }
            });
        },

        // 保存标题
        async saveTitle() {
            const newTitle = this.editingTitleValue.trim();
            if (!newTitle || newTitle === this.displayTitle || !this.sessionId) {
                this.cancelEditTitle();
                return;
            }

            // 验证会话标题更新
            if (!this.validateSessionTitleUpdate(this.sessionId, newTitle)) {
                this.cancelEditTitle();
                return;
            }

            try {
                await this.updateSessionTitle(this.sessionId, newTitle);
                // 通知父组件标题已更新，传递新标题
                this.$emit('title-updated', newTitle);
            } catch (error) {
                this.handleSessionTitleUpdateError(error);
            }

            this.cancelEditTitle();
        },

        // 验证会话标题更新
        validateSessionTitleUpdate(sessionId, newTitle) {
            if (!this.isStorageAvailable || !sessionId || !newTitle) {
                return false;
            }

            // 不允许更新临时会话的标题
            if (sessionId.startsWith('temp_')) {
                console.warn('不能更新临时会话的标题');
                return false;
            }

            return true;
        },

        // 更新会话标题
        async updateSessionTitle(sessionId, newTitle) {
            if (!this.chatStorageService) {
                throw new Error('存储服务不可用');
            }

            try {
                await this.chatStorageService.updateSession(sessionId, {
                    title: newTitle
                });
            } catch (error) {
                throw new Error('更新标题失败: ' + error.message);
            }
        },

        // 处理会话标题更新错误
        handleSessionTitleUpdateError(error) {
            console.error('更新会话标题失败:', error);
            if (this.$message) {
                this.$message.error('更新标题失败，请重试');
            }
        },

        // 取消编辑标题
        cancelEditTitle() {
            this.isEditingTitle = false;
            this.editingTitleValue = '';
        }
    },
    mounted() {
        // 添加滚动事件监听器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.addEventListener('scroll', this.handleScroll);
        }
    },
    beforeDestroy() {
        // 清理滚动事件监听器和定时器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
        if (this.scrollThrottleTimer) {
            clearTimeout(this.scrollThrottleTimer);
        }
    }
};
</script>

<style scoped lang="less">
.chat-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;

    .chat-title-section {
        width: 60rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 1rem;

        .chat-title {
            font-weight: 500;
            font-size: 16px;
            color: #222222;
            line-height: 22px;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            max-width: 100%;

            &:hover {
                background: rgba(0, 0, 0, 0.05);

                .edit-icon {
                    opacity: 1;
                }
            }

            &.temporary-session {
                color: #999;
                cursor: default;
                font-style: italic;

                &:hover {
                    background: transparent;
                }
            }

            .edit-icon {
                opacity: 0;
                transition: opacity 0.2s ease;
                font-size: 14px;
                color: #666;
            }
        }

        .chat-title-editor {
            max-width: 20rem;
            width: 100%;

            /deep/ .el-input__inner {
                text-align: center;
                font-weight: 500;
                font-size: 16px;
                border: 1px solid #0265fe;
                border-radius: 0.25rem;

                &:focus {
                    border-color: #0265fe;
                    box-shadow: 0 0 0 2px rgba(2, 101, 254, 0.2);
                }
            }
        }
    }

    .chat-container {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin: 0 auto;
        width: 100%;

        .chat-messages {
            --bar-width: 0;
            --bar-height: 0;
            width: 60rem;
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message-item {
                display: flex;

                &.user-message {
                    justify-content: flex-end;

                    .message-content {
                        background: #cde1ff;
                        border-radius: 0.75rem 0.25rem 0.75rem 0.75rem;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.assistant-message {
                    justify-content: flex-start;

                    .message-content {
                        width: fit-content;
                        max-width: 100%;
                        background: transparent;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.error-message {
                    justify-content: flex-start;

                    .message-content {
                        background: #fef2f2;
                        border: 1px solid #fecaca;
                        color: #dc2626;
                        max-width: 70%;
                    }
                }

                .message-content {
                    padding: 0.75rem 1rem;
                    border-radius: 0.75rem;

                    .message-text {
                        font-size: 0.875rem;
                        line-height: 1.6;
                        color: #333;
                    }

                    .message-time {
                        margin-top: 0.25rem;
                        font-size: 0.75rem;
                        opacity: 0.7;
                    }

                    // AI回复加载状态样式
                    &.loading-message {
                        display: flex;
                        flex-direction: column;
                        gap: 1rem;

                        .typing-text {
                            font-weight: 400;
                            font-size: 1rem;
                            color: #222222;
                            line-height: 1.5rem;
                        }

                        .typing-dots {
                            display: flex;
                            gap: 0.5rem;

                            .dot {
                                width: 0.5rem;
                                height: 0.5rem;
                                background: #666;
                                border-radius: 50%;
                                animation: typing 1.4s infinite ease-in-out;

                                &:nth-child(1) {
                                    animation-delay: 0s;
                                }
                                &:nth-child(2) {
                                    animation-delay: 0.2s;
                                }
                                &:nth-child(3) {
                                    animation-delay: 0.4s;
                                }
                            }
                        }

                        .stop-generation-btn {
                            width: fit-content;
                            margin-top: 0.5rem;
                            font-family: HONORSansCN, HONORSansCN;
                            font-weight: 400;
                            font-size: 1rem;
                            color: #0265fe;
                            line-height: 1.5rem;
                            cursor: pointer;
                            transition: color 0.2s;

                            &:hover {
                                color: lighten(#0265fe, 20%);
                            }
                        }
                    }
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    margin-bottom: 1rem;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .empty-text {
                    color: #999;
                    font-size: 0.875rem;
                }
            }
        }

        .chat-input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;
            position: relative;
            .back-bottom {
                width: 2rem;
                height: 2rem;
                position: absolute;
                top: -1.25rem;
                right: 0.625rem;
                transform: translateY(-100%);
                cursor: pointer;
                border-radius: 50%;
                background-color: #fff;
                box-shadow: inset 0 0 0 0.0625rem darken(#fff, 5%);
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease-in-out;
                // 默认隐藏状态
                opacity: 0;
                visibility: hidden;
                transform: translateY(-100%) scale(0.8);

                // 显示状态
                &.is-show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(-100%) scale(1);
                }

                &:hover {
                    background-color: darken(#fff, 5%);
                    transform: translateY(-100%) scale(1.05);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }

                .el-icon-arrow-down {
                    font-size: 1rem;
                    font-weight: 600;
                    transition: transform 0.2s ease;
                }

                &:active {
                    transform: translateY(-100%) scale(0.95);
                }
            }
        }
    }
}

// 打字动画关键帧
@keyframes typing {
    0%,
    60%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

// 消息文本容器
.message-text-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

// 流式文本样式
.streaming-text {
    position: relative;
    display: inline-block;
}

// 打字光标样式
.typing-cursor {
    display: inline-block;
    animation: blink 1s infinite;
    color: #0265fe;
    font-weight: bold;
    margin-left: 1px;
    vertical-align: baseline;
    font-size: 1em;
    line-height: 1;
}

// 内联光标样式（跟随文本）
.cursor-inline {
    position: relative;
    margin-left: 2px;
}

// 空消息占位符
.empty-message-placeholder {
    min-height: 20px;
    display: flex;
    align-items: center;
}

@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0;
    }
}
</style>
<!-- Markdown相关样式已迁移到MarkdownRenderer组件 -->
