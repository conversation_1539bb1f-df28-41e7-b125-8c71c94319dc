/**
 * 聊天存储服务类
 * 支持localStorage和IndexedDB两种存储方案
 * 提供会话和消息的完整CRUD操作
 */
import LocalStorageAdapter from '../utils/storage/LocalStorageAdapter';
import IndexedDBAdapter from '../utils/storage/IndexedDBAdapter';
import { STORAGE_CONFIG } from '../constant/appConfig';

class ChatStorageService {
    constructor(config = {}) {
        const storageType = config.storageType || 'localStorage';
        const storageDefaults = STORAGE_CONFIG[storageType] || STORAGE_CONFIG.localStorage;

        this.config = {
            // 使用配置文件中的默认值
            storageType: storageType,
            maxSessions: storageDefaults.maxSessions,
            maxMessagesPerSession: storageDefaults.maxMessagesPerSession,
            autoCleanup: storageDefaults.autoCleanup,
            cleanupDays: storageDefaults.cleanupDays,
            // 覆盖用户自定义配置
            ...config
        };

        this.storage = null;
        this.isInitialized = false;
    }

    /**
     * 初始化存储服务
     */
    async init() {
        try {
            if (this.config.storageType === 'indexedDB') {
                this.storage = new IndexedDBAdapter();
            } else {
                this.storage = new LocalStorageAdapter();
            }

            await this.storage.init();
            this.isInitialized = true;

            // 执行自动清理
            if (this.config.autoCleanup) {
                await this.performAutoCleanup();
            }

            return true;
        } catch (error) {
            console.error('存储服务初始化失败:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * 检查存储服务是否可用
     */
    isAvailable() {
        return this.isInitialized && this.storage !== null;
    }

    // ==================== 会话管理 ====================

    /**
     * 创建新会话
     */
    async createSession(sessionData = {}) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const session = {
            id: this.generateId(),
            title: sessionData.title || '新对话',
            createdAt: Date.now(),
            updatedAt: Date.now(),
            model: sessionData.model || '',
            messageCount: 0,
            ...sessionData
        };

        await this.storage.saveSession(session);

        // 设置为当前会话
        if (this.storage.setCurrentSessionId) {
            this.storage.setCurrentSessionId(session.id);
        }

        return session;
    }

    /**
     * 获取会话信息
     */
    async getSession(sessionId) {
        if (!this.isAvailable()) {
            return null;
        }

        return await this.storage.getSession(sessionId);
    }

    /**
     * 获取完整会话数据（包含消息）
     */
    async getSessionWithMessages(sessionId) {
        if (!this.isAvailable()) {
            return null;
        }

        // 在合并存储中，会话本身就包含消息
        return await this.storage.getSession(sessionId);
    }

    /**
     * 获取当前会话ID
     */
    getCurrentSessionId() {
        if (!this.isAvailable()) {
            return null;
        }

        if (this.storage.getCurrentSessionId) {
            return this.storage.getCurrentSessionId();
        }

        return null;
    }

    /**
     * 设置当前会话ID
     */
    setCurrentSessionId(sessionId) {
        if (!this.isAvailable()) {
            return;
        }

        if (this.storage.setCurrentSessionId) {
            this.storage.setCurrentSessionId(sessionId);
        }
    }

    /**
     * 获取所有会话及其消息
     */
    async getAllSessionsWithMessages() {
        if (!this.isAvailable()) {
            return [];
        }

        // 在合并存储中，会话本身就包含消息
        const sessions = await this.storage.getAllSessions();
        return sessions.sort((a, b) => b.updatedAt - a.updatedAt);
    }

    /**
     * 更新会话信息
     */
    async updateSession(sessionId, updates) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const session = await this.storage.getSession(sessionId);
        if (!session) {
            throw new Error('会话不存在');
        }

        const updatedSession = {
            ...session,
            ...updates,
            updatedAt: Date.now()
        };

        await this.storage.saveSession(updatedSession);
        return updatedSession;
    }

    /**
     * 删除会话
     */
    async deleteSession(sessionId) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        // 删除会话相关的所有消息
        await this.storage.deleteMessagesBySession(sessionId);

        // 删除会话
        await this.storage.deleteSession(sessionId);
    }

    /**
     * 获取所有会话列表
     */
    async getAllSessions() {
        if (!this.isAvailable()) {
            return [];
        }

        const sessions = await this.storage.getAllSessions();

        // 按更新时间倒序排列
        return sessions.sort((a, b) => b.updatedAt - a.updatedAt);
    }

    // ==================== 消息管理 ====================

    /**
     * 添加消息
     */
    async addMessage(sessionId, messageData) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const message = {
            id: messageData.id || this.generateId(), // 使用传入的ID或生成新ID
            sessionId: sessionId,
            role: messageData.role, // 'user' | 'assistant'
            content: messageData.content,
            timestamp: messageData.timestamp || Date.now(),
            model: messageData.model || '',
            ...messageData
        };

        await this.storage.saveMessage(message);

        // 更新会话的消息计数和更新时间
        const session = await this.storage.getSession(sessionId);
        if (session) {
            await this.updateSession(sessionId, {
                messageCount: session.messageCount + 1,
                title: this.generateSessionTitle(message, session)
            });
        }

        return message;
    }

    /**
     * 获取会话的所有消息
     */
    async getMessages(sessionId) {
        if (!this.isAvailable()) {
            return [];
        }

        const messages = await this.storage.getMessagesBySession(sessionId);

        // 按时间戳排序
        return messages.sort((a, b) => a.timestamp - b.timestamp);
    }

    /**
     * 删除消息
     */
    async deleteMessage(messageId) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const message = await this.storage.getMessage(messageId);
        if (message) {
            await this.storage.deleteMessage(messageId);

            // 更新会话的消息计数
            const session = await this.storage.getSession(message.sessionId);
            if (session && session.messageCount > 0) {
                await this.updateSession(message.sessionId, {
                    messageCount: session.messageCount - 1
                });
            }
        }
    }

    // ==================== 数据管理 ====================

    /**
     * 导出所有数据
     */
    async exportData() {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const sessions = await this.getAllSessions();
        const allMessages = [];

        for (const session of sessions) {
            const messages = await this.getMessages(session.id);
            allMessages.push(...messages);
        }

        return {
            version: '1.0',
            exportTime: Date.now(),
            sessions: sessions,
            messages: allMessages
        };
    }

    /**
     * 导入数据
     */
    async importData(data) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        if (!data.sessions || !data.messages) {
            throw new Error('数据格式不正确');
        }

        // 导入会话
        for (const session of data.sessions) {
            await this.storage.saveSession(session);
        }

        // 导入消息
        for (const message of data.messages) {
            await this.storage.saveMessage(message);
        }
    }

    /**
     * 清空所有数据
     */
    async clearAllData() {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        await this.storage.clear();
    }

    /**
     * 获取存储信息
     */
    async getStorageInfo() {
        if (!this.isAvailable()) {
            return null;
        }

        const sessions = await this.getAllSessions();
        let totalMessages = 0;

        for (const session of sessions) {
            totalMessages += session.messageCount || 0;
        }

        return {
            storageType: this.config.storageType,
            sessionCount: sessions.length,
            messageCount: totalMessages,
            isAvailable: this.isAvailable()
        };
    }

    // ==================== 工具方法 ====================

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * 生成会话标题
     */
    generateSessionTitle(firstMessage, session) {
        if (session.messageCount > 0) {
            return session.title; // 保持现有标题
        }

        if (firstMessage.role === 'user') {
            // 取用户消息的前20个字符作为标题
            const title = firstMessage.content.trim().substring(0, 20);
            if (firstMessage.content.length > 20) {
                return title + '...';
            }
            return title;
        }

        return '新对话';
    }

    /**
     * 自动清理过期数据
     */
    async performAutoCleanup() {
        if (!this.isAvailable()) {
            return;
        }

        const cutoffTime = Date.now() - (this.config.cleanupDays * 24 * 60 * 60 * 1000);
        const sessions = await this.getAllSessions();

        for (const session of sessions) {
            if (session.updatedAt < cutoffTime) {
                await this.deleteSession(session.id);
            }
        }
    }

    /**
     * 搜索消息
     */
    async searchMessages(keyword, sessionId = null) {
        if (!this.isAvailable()) {
            return [];
        }

        try {
            if (this.storage.searchMessages) {
                // IndexedDB支持高级搜索
                return await this.storage.searchMessages(keyword, sessionId);
            }
            // localStorage的简单搜索
            if (sessionId) {
                // 搜索特定会话的消息
                const sessionMessages = await this.storage.getMessagesBySession(sessionId);
                return sessionMessages.filter(msg =>
                    msg.content.toLowerCase().includes(keyword.toLowerCase())
                ).sort((a, b) => b.timestamp - a.timestamp);
            }
            // 搜索所有消息（仅支持localStorage）
            if (this.config.storageType === 'localStorage') {
                try {
                    const allMessages = this.storage.getMessages();
                    const results = Object.values(allMessages).filter(msg => {
                        const matchesKeyword = msg.content.toLowerCase().includes(keyword.toLowerCase());
                        return matchesKeyword;
                    });
                    return results.sort((a, b) => b.timestamp - a.timestamp);
                } catch (error) {
                    // 如果方法不存在，返回空数组
                    return [];
                }
            }
            // IndexedDB或其他存储类型不支持全局搜索
            return [];
        } catch (error) {
            console.error('搜索消息失败:', error);
            return [];
        }
    }

    /**
     * 获取统计信息
     */
    async getStatistics() {
        if (!this.isAvailable()) {
            return null;
        }

        try {
            const sessions = await this.getAllSessions();
            const storageInfo = await this.getStorageInfo();

            let totalMessages = 0;
            let totalCharacters = 0;
            const modelUsage = {};

            for (const session of sessions) {
                const messages = await this.getMessages(session.id);
                totalMessages += messages.length;

                messages.forEach(msg => {
                    totalCharacters += msg.content.length;
                    if (msg.model) {
                        modelUsage[msg.model] = (modelUsage[msg.model] || 0) + 1;
                    }
                });
            }

            let oldestSession = null;
            let newestSession = null;

            if (sessions.length > 0) {
                oldestSession = Math.min(...sessions.map(s => s.createdAt));
                newestSession = Math.max(...sessions.map(s => s.updatedAt));
            }

            return {
                sessionCount: sessions.length,
                messageCount: totalMessages,
                totalCharacters: totalCharacters,
                modelUsage: modelUsage,
                storageInfo: storageInfo,
                oldestSession: oldestSession,
                newestSession: newestSession
            };
        } catch (error) {
            console.error('获取统计信息失败:', error);
            return null;
        }
    }

    /**
     * 批量操作：删除多个会话
     */
    async deleteSessions(sessionIds) {
        if (!this.isAvailable()) {
            throw new Error('存储服务未初始化');
        }

        const results = [];
        for (const sessionId of sessionIds) {
            try {
                await this.deleteSession(sessionId);
                results.push({ sessionId, success: true });
            } catch (error) {
                results.push({ sessionId, success: false, error: error.message });
            }
        }

        return results;
    }

    /**
     * 备份数据到文件
     */
    async backupToFile() {
        try {
            const data = await this.exportData();
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('备份失败:', error);
            throw error;
        }
    }

    /**
     * 从文件恢复数据
     */
    async restoreFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = async (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    await this.importData(data);
                    resolve(true);
                } catch (error) {
                    reject(new Error('文件格式不正确或导入失败'));
                }
            };

            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };

            reader.readAsText(file);
        });
    }

    /**
     * 销毁服务实例
     */
    destroy() {
        if (this.storage && this.storage.close) {
            this.storage.close();
        }
        this.storage = null;
        this.isInitialized = false;
    }
}

export default ChatStorageService;
